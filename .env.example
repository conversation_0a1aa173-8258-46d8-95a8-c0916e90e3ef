# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=1000

# Financial API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key

# Qdrant Cloud Configuration
QDRANT_URL=https://your-cluster-url.qdrant.tech
QDRANT_API_KEY=your_qdrant_cloud_api_key
QDRANT_COLLECTION_NAME=financial_documents

# Redis Configuration (if using Redis Cloud)
REDIS_URL=redis://your-redis-cloud-url:port

# Application Settings
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=5
CACHE_TTL_SECONDS=3600
API_RATE_LIMIT_PER_MINUTE=50
ENVIRONMENT=development

# Rate Limiting Settings
ALPHA_VANTAGE_REQUESTS_PER_DAY=25
FINNHUB_REQUESTS_PER_MINUTE=60
OPENAI_REQUESTS_PER_MINUTE=50

# Vector Database Settings
EMBEDDING_DIMENSION=1536
SIMILARITY_THRESHOLD=0.75
CHUNK_SIZE=800
CHUNK_OVERLAP=150
