"""
FastAPI Main Application
Entry point for the Financial Analyst Chatbot API
"""
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from ..config.settings import get_settings
from ..config.logging_config import setup_logging, get_logger
from ..query_processing.parser import QueryParser
from ..query_processing.executor import QueryExecutor
from ..query_processing.response_generator import ResponseGenerator
from ..utils.data_source_manager import DataSourceManager
from .models import (
    ChatRequest, ChatResponse, HealthResponse, ErrorResponse,
    StockPriceRequest, StockPriceResponse,
    CompanyFundamentalsRequest, FinancialStatementsRequest,
    ComparisonRequest, HistoricalDataRequest
)
from .dependencies import verify_api_key


# Setup logging
setup_logging()
logger = get_logger("main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Financial Analyst Chatbot API")
    settings = get_settings()

    # Initialize query processing pipeline
    try:
        app.state.data_manager = DataSourceManager()
        app.state.query_parser = QueryParser()
        app.state.query_executor = QueryExecutor(app.state.data_manager)
        app.state.response_generator = ResponseGenerator()

        logger.info("Query processing pipeline initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize query processing pipeline: {str(e)}")
        raise

    logger.info(f"API started successfully on {settings.environment} environment")

    yield

    # Shutdown
    logger.info("Shutting down Financial Analyst Chatbot API")


# Create FastAPI application
app = FastAPI(
    title="Financial Analyst Chatbot",
    description="Multi-source financial data integration with intelligent query routing",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for frontend
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return ErrorResponse(
        error="Internal server error",
        error_code="INTERNAL_ERROR",
        details={"message": str(exc)} if get_settings().debug else None
    )


@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main frontend page"""
    try:
        with open("static/index.html", "r") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(
            content="<h1>Financial Analyst Chatbot</h1><p>Frontend not yet available</p>"
        )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    settings = get_settings()

    # Check service health
    services = {
        "api": "healthy",
        "openai": "unknown",
        "qdrant": "unknown",
        "alpha_vantage": "unknown",
        "finnhub": "unknown"
    }

    # Check data source health if available
    try:
        if hasattr(app.state, 'data_manager'):
            source_status = app.state.data_manager.get_source_status()
            for source_name, status in source_status.items():
                service_key = source_name.lower().replace(" ", "_")
                services[service_key] = status.get("status", "unknown")
    except Exception as e:
        logger.warning(f"Health check error: {str(e)}")

    return HealthResponse(
        status="healthy",
        version="1.0.0",
        services=services
    )


@app.post("/api/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    settings = Depends(verify_api_key)
):
    """Main chat endpoint for financial queries"""
    start_time = time.time()

    logger.info(f"Processing chat query: {request.query[:100]}...")

    try:
        # Parse the query
        parsed_query = await app.state.query_parser.parse_query(request.query)

        # Execute the query
        execution_result = await app.state.query_executor.execute_query(parsed_query)

        # Generate response
        response_text = await app.state.response_generator.generate_response(
            parsed_query, execution_result
        )

        # Prepare data sources info
        data_sources = []
        for source_name in execution_result.sources_used:
            data_sources.append({
                "name": source_name,
                "type": "primary",  # Could be enhanced with actual type
                "cached": False,  # Could be enhanced with cache info
                "response_time_ms": None,
                "error": None
            })

        response = ChatResponse(
            response=response_text,
            query_type=parsed_query.query_type.value,
            data_sources=data_sources,
            processing_time_ms=round((time.time() - start_time) * 1000, 2),
            session_id=request.session_id
        )

        logger.info(f"Chat query processed successfully in {response.processing_time_ms}ms")
        return response

    except Exception as e:
        logger.error(f"Error processing chat query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing query: {str(e)}"
        )


@app.post("/api/stock-price", response_model=StockPriceResponse)
async def get_stock_price(
    request: StockPriceRequest,
    settings = Depends(verify_api_key)
):
    """Get current stock price"""
    logger.info(f"Getting stock price for {request.symbol}")

    try:
        # Use data manager to get stock price
        response = await app.state.data_manager.get_stock_price(request.symbol)

        if not response.success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Could not retrieve stock price for {request.symbol}: {response.error}"
            )

        # Convert to API response format
        data = response.data
        return StockPriceResponse(
            symbol=data.get("symbol", request.symbol),
            price=data.get("price", 0.0),
            currency="USD",
            change=data.get("change"),
            change_percent=data.get("change_percent"),
            timestamp=response.timestamp,
            source=response.source
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stock price: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving stock price: {str(e)}"
        )


@app.post("/api/company-fundamentals")
async def get_company_fundamentals(
    request: CompanyFundamentalsRequest,
    settings = Depends(verify_api_key)
):
    """Get company fundamental data"""
    logger.info(f"Getting fundamentals for {request.symbol}")
    
    try:
        # TODO: Implement actual fundamentals retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Company fundamentals endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting company fundamentals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving company fundamentals: {str(e)}"
        )


@app.post("/api/financial-statements")
async def get_financial_statements(
    request: FinancialStatementsRequest,
    settings = Depends(verify_api_key)
):
    """Get financial statements"""
    logger.info(f"Getting {request.statement_type} statement for {request.symbol}")
    
    try:
        # TODO: Implement actual financial statements retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Financial statements endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting financial statements: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving financial statements: {str(e)}"
        )


@app.post("/api/compare")
async def compare_companies(
    request: ComparisonRequest,
    settings = Depends(verify_api_key)
):
    """Compare multiple companies"""
    logger.info(f"Comparing companies: {', '.join(request.symbols)}")
    
    try:
        # TODO: Implement actual company comparison
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Company comparison endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing companies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error comparing companies: {str(e)}"
        )


@app.post("/api/historical-data")
async def get_historical_data(
    request: HistoricalDataRequest,
    settings = Depends(verify_api_key)
):
    """Get historical stock data"""
    logger.info(f"Getting historical data for {request.symbol}")
    
    try:
        # TODO: Implement actual historical data retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Historical data endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting historical data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving historical data: {str(e)}"
        )


@app.get("/api/stats")
async def get_system_stats():
    """Get system statistics and performance metrics"""
    try:
        stats = {}

        # Data source statistics
        if hasattr(app.state, 'data_manager'):
            stats["data_sources"] = app.state.data_manager.get_source_status()
            stats["cache"] = app.state.data_manager.get_cache_stats()

        # Query execution statistics
        if hasattr(app.state, 'query_executor'):
            stats["execution"] = app.state.query_executor.get_execution_stats()

        # Router statistics
        if hasattr(app.state, 'data_manager') and hasattr(app.state.data_manager, 'router'):
            stats["routing"] = app.state.data_manager.router.get_routing_statistics()

        return stats

    except Exception as e:
        logger.error(f"Error getting system stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving system statistics: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
