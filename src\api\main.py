"""
FastAPI Main Application
Entry point for the Financial Analyst Chatbot API
"""
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from ..config.settings import get_settings
from ..config.logging_config import setup_logging, get_logger
from .models import (
    ChatRequest, ChatResponse, HealthResponse, ErrorResponse,
    StockPriceRequest, StockPriceResponse,
    CompanyFundamentalsRequest, FinancialStatementsRequest,
    ComparisonRequest, HistoricalDataRequest
)
from .dependencies import verify_api_key


# Setup logging
setup_logging()
logger = get_logger("main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Financial Analyst Chatbot API")
    settings = get_settings()
    
    # Initialize services here
    # TODO: Initialize data sources, vector DB, cache, etc.
    
    logger.info(f"API started successfully on {settings.environment} environment")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Financial Analyst Chatbot API")


# Create FastAPI application
app = FastAPI(
    title="Financial Analyst Chatbot",
    description="Multi-source financial data integration with intelligent query routing",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for frontend
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return ErrorResponse(
        error="Internal server error",
        error_code="INTERNAL_ERROR",
        details={"message": str(exc)} if get_settings().debug else None
    )


@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main frontend page"""
    try:
        with open("static/index.html", "r") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(
            content="<h1>Financial Analyst Chatbot</h1><p>Frontend not yet available</p>"
        )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    settings = get_settings()
    
    # Check service health
    services = {
        "api": "healthy",
        "openai": "unknown",  # TODO: Implement actual health checks
        "qdrant": "unknown",
        "alpha_vantage": "unknown",
        "finnhub": "unknown"
    }
    
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        services=services
    )


@app.post("/api/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    settings = Depends(verify_api_key)
):
    """Main chat endpoint for financial queries"""
    start_time = time.time()
    
    logger.info(f"Processing chat query: {request.query[:100]}...")
    
    try:
        # TODO: Implement actual query processing
        # This is a placeholder response
        response = ChatResponse(
            response=f"I received your query: '{request.query}'. The full query processing system is being implemented.",
            query_type="general",
            data_sources=[],
            processing_time_ms=round((time.time() - start_time) * 1000, 2),
            session_id=request.session_id
        )
        
        logger.info(f"Chat query processed successfully in {response.processing_time_ms}ms")
        return response
        
    except Exception as e:
        logger.error(f"Error processing chat query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing query: {str(e)}"
        )


@app.post("/api/stock-price", response_model=StockPriceResponse)
async def get_stock_price(
    request: StockPriceRequest,
    settings = Depends(verify_api_key)
):
    """Get current stock price"""
    logger.info(f"Getting stock price for {request.symbol}")
    
    try:
        # TODO: Implement actual stock price retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Stock price endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stock price: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving stock price: {str(e)}"
        )


@app.post("/api/company-fundamentals")
async def get_company_fundamentals(
    request: CompanyFundamentalsRequest,
    settings = Depends(verify_api_key)
):
    """Get company fundamental data"""
    logger.info(f"Getting fundamentals for {request.symbol}")
    
    try:
        # TODO: Implement actual fundamentals retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Company fundamentals endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting company fundamentals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving company fundamentals: {str(e)}"
        )


@app.post("/api/financial-statements")
async def get_financial_statements(
    request: FinancialStatementsRequest,
    settings = Depends(verify_api_key)
):
    """Get financial statements"""
    logger.info(f"Getting {request.statement_type} statement for {request.symbol}")
    
    try:
        # TODO: Implement actual financial statements retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Financial statements endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting financial statements: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving financial statements: {str(e)}"
        )


@app.post("/api/compare")
async def compare_companies(
    request: ComparisonRequest,
    settings = Depends(verify_api_key)
):
    """Compare multiple companies"""
    logger.info(f"Comparing companies: {', '.join(request.symbols)}")
    
    try:
        # TODO: Implement actual company comparison
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Company comparison endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing companies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error comparing companies: {str(e)}"
        )


@app.post("/api/historical-data")
async def get_historical_data(
    request: HistoricalDataRequest,
    settings = Depends(verify_api_key)
):
    """Get historical stock data"""
    logger.info(f"Getting historical data for {request.symbol}")
    
    try:
        # TODO: Implement actual historical data retrieval
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Historical data endpoint not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting historical data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving historical data: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
