{"timestamp": "2025-06-05T09:28:40.900820Z", "level": "INFO", "logger": "financial_chatbot.test", "message": "Test log message", "module": "test_phase1", "function": "test_logging_system", "line": 81, "taskName": "Task-1", "asctime": "2025-06-05 14:58:40,900"}
{"timestamp": "2025-06-05T09:28:40.901825Z", "level": "ERROR", "logger": "financial_chatbot.test", "message": "Test error message", "module": "test_phase1", "function": "test_logging_system", "line": 82, "taskName": "Task-1", "asctime": "2025-06-05 14:58:40,901"}
{"timestamp": "2025-06-05T09:29:52.656952Z", "level": "INFO", "logger": "httpx", "message": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "taskName": "Task-1", "asctime": "2025-06-05 14:59:52,656"}
{"timestamp": "2025-06-05T09:29:52.661958Z", "level": "INFO", "logger": "httpx", "message": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "taskName": "Task-1", "asctime": "2025-06-05 14:59:52,660"}
{"timestamp": "2025-06-05T09:29:52.666957Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "Processing chat query: Test query...", "module": "main", "function": "chat", "line": 120, "taskName": "anyio.from_thread.BlockingPortal._call_func", "asctime": "2025-06-05 14:59:52,666"}
{"timestamp": "2025-06-05T09:29:52.667957Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "Chat query processed successfully in 1.0ms", "module": "main", "function": "chat", "line": 133, "taskName": "anyio.from_thread.BlockingPortal._call_func", "asctime": "2025-06-05 14:59:52,667"}
{"timestamp": "2025-06-05T09:29:52.668953Z", "level": "INFO", "logger": "httpx", "message": "HTTP Request: POST http://testserver/api/chat \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "taskName": "Task-1", "asctime": "2025-06-05 14:59:52,668"}
{"timestamp": "2025-06-05T09:30:45.719769Z", "level": "INFO", "logger": "uvicorn.error", "message": "Started server process [7004]", "module": "server", "function": "serve", "line": 76, "taskName": "Task-1", "color_message": "Started server process [\u001b[36m%d\u001b[0m]", "asctime": "2025-06-05 15:00:45,719"}
{"timestamp": "2025-06-05T09:30:45.719769Z", "level": "INFO", "logger": "uvicorn.error", "message": "Waiting for application startup.", "module": "on", "function": "startup", "line": 46, "taskName": "Task-1", "asctime": "2025-06-05 15:00:45,719"}
{"timestamp": "2025-06-05T09:30:45.720783Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "Starting Financial Analyst Chatbot API", "module": "main", "function": "lifespan", "line": 33, "taskName": "Task-2", "asctime": "2025-06-05 15:00:45,720"}
{"timestamp": "2025-06-05T09:30:45.720783Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "API started successfully on test environment", "module": "main", "function": "lifespan", "line": 39, "taskName": "Task-2", "asctime": "2025-06-05 15:00:45,720"}
{"timestamp": "2025-06-05T09:30:45.720783Z", "level": "INFO", "logger": "uvicorn.error", "message": "Application startup complete.", "module": "on", "function": "startup", "line": 60, "taskName": "Task-1", "asctime": "2025-06-05 15:00:45,720"}
{"timestamp": "2025-06-05T09:30:45.721781Z", "level": "INFO", "logger": "uvicorn.error", "message": "Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)", "module": "server", "function": "_log_started_message", "line": 218, "taskName": "Task-1", "color_message": "Uvicorn running on \u001b[1m%s://%s:%d\u001b[0m (Press CTRL+C to quit)", "asctime": "2025-06-05 15:00:45,721"}
{"timestamp": "2025-06-05T10:07:10.524905Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:10,524"}
{"timestamp": "2025-06-05T10:07:11.239842Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:11,239"}
{"timestamp": "2025-06-05T10:07:15.172433Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for PRICE: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for PRICE: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:15,170"}
{"timestamp": "2025-06-05T10:07:15.487665Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:15,486"}
{"timestamp": "2025-06-05T10:07:15.746958Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:15,746"}
{"timestamp": "2025-06-05T10:07:17.876903Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for IS: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for IS: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:17,875"}
{"timestamp": "2025-06-05T10:07:18.193937Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:18,193"}
{"timestamp": "2025-06-05T10:07:18.447357Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:18,447"}
{"timestamp": "2025-06-05T10:07:20.592616Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for APPLE: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for APPLE: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:20,592"}
{"timestamp": "2025-06-05T10:07:20.904974Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:20,904"}
{"timestamp": "2025-06-05T10:07:21.169259Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:21,169"}
{"timestamp": "2025-06-05T10:07:23.700778Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for S: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for S: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:23,700"}
{"timestamp": "2025-06-05T10:07:26.505249Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for AAPL: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for AAPL: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:26,505"}
