{"timestamp": "2025-06-05T09:28:40.900820Z", "level": "INFO", "logger": "financial_chatbot.test", "message": "Test log message", "module": "test_phase1", "function": "test_logging_system", "line": 81, "taskName": "Task-1", "asctime": "2025-06-05 14:58:40,900"}
{"timestamp": "2025-06-05T09:28:40.901825Z", "level": "ERROR", "logger": "financial_chatbot.test", "message": "Test error message", "module": "test_phase1", "function": "test_logging_system", "line": 82, "taskName": "Task-1", "asctime": "2025-06-05 14:58:40,901"}
{"timestamp": "2025-06-05T09:29:52.656952Z", "level": "INFO", "logger": "httpx", "message": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "taskName": "Task-1", "asctime": "2025-06-05 14:59:52,656"}
{"timestamp": "2025-06-05T09:29:52.661958Z", "level": "INFO", "logger": "httpx", "message": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "taskName": "Task-1", "asctime": "2025-06-05 14:59:52,660"}
{"timestamp": "2025-06-05T09:29:52.666957Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "Processing chat query: Test query...", "module": "main", "function": "chat", "line": 120, "taskName": "anyio.from_thread.BlockingPortal._call_func", "asctime": "2025-06-05 14:59:52,666"}
{"timestamp": "2025-06-05T09:29:52.667957Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "Chat query processed successfully in 1.0ms", "module": "main", "function": "chat", "line": 133, "taskName": "anyio.from_thread.BlockingPortal._call_func", "asctime": "2025-06-05 14:59:52,667"}
{"timestamp": "2025-06-05T09:29:52.668953Z", "level": "INFO", "logger": "httpx", "message": "HTTP Request: POST http://testserver/api/chat \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "taskName": "Task-1", "asctime": "2025-06-05 14:59:52,668"}
{"timestamp": "2025-06-05T09:30:45.719769Z", "level": "INFO", "logger": "uvicorn.error", "message": "Started server process [7004]", "module": "server", "function": "serve", "line": 76, "taskName": "Task-1", "color_message": "Started server process [\u001b[36m%d\u001b[0m]", "asctime": "2025-06-05 15:00:45,719"}
{"timestamp": "2025-06-05T09:30:45.719769Z", "level": "INFO", "logger": "uvicorn.error", "message": "Waiting for application startup.", "module": "on", "function": "startup", "line": 46, "taskName": "Task-1", "asctime": "2025-06-05 15:00:45,719"}
{"timestamp": "2025-06-05T09:30:45.720783Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "Starting Financial Analyst Chatbot API", "module": "main", "function": "lifespan", "line": 33, "taskName": "Task-2", "asctime": "2025-06-05 15:00:45,720"}
{"timestamp": "2025-06-05T09:30:45.720783Z", "level": "INFO", "logger": "financial_chatbot.main", "message": "API started successfully on test environment", "module": "main", "function": "lifespan", "line": 39, "taskName": "Task-2", "asctime": "2025-06-05 15:00:45,720"}
{"timestamp": "2025-06-05T09:30:45.720783Z", "level": "INFO", "logger": "uvicorn.error", "message": "Application startup complete.", "module": "on", "function": "startup", "line": 60, "taskName": "Task-1", "asctime": "2025-06-05 15:00:45,720"}
{"timestamp": "2025-06-05T09:30:45.721781Z", "level": "INFO", "logger": "uvicorn.error", "message": "Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)", "module": "server", "function": "_log_started_message", "line": 218, "taskName": "Task-1", "color_message": "Uvicorn running on \u001b[1m%s://%s:%d\u001b[0m (Press CTRL+C to quit)", "asctime": "2025-06-05 15:00:45,721"}
