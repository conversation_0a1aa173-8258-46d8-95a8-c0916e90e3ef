"""
Structured Logging Configuration
Provides JSON-formatted logging with comprehensive debugging and monitoring
"""
import logging
import logging.config
import json
import sys
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from .settings import get_settings


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in {
                "name", "msg", "args", "levelname", "levelno", "pathname",
                "filename", "module", "lineno", "funcName", "created",
                "msecs", "relativeCreated", "thread", "threadName",
                "processName", "process", "getMessage", "exc_info", "exc_text",
                "stack_info"
            }:
                log_entry[key] = value
        
        return json.dumps(log_entry, default=str)


def setup_logging() -> None:
    """Setup application logging configuration"""
    settings = get_settings()
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Logging configuration
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": JSONFormatter,
            },
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.log_level,
                "formatter": "json" if settings.environment == "production" else "standard",
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.log_level,
                "formatter": "json",
                "filename": "logs/financial_chatbot.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "json",
                "filename": "logs/errors.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console", "file"],
                "level": settings.log_level,
                "propagate": False,
            },
            "financial_chatbot": {
                "handlers": ["console", "file", "error_file"],
                "level": settings.log_level,
                "propagate": False,
            },
            "uvicorn": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "fastapi": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }
    
    logging.config.dictConfig(config)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name"""
    return logging.getLogger(f"financial_chatbot.{name}")


def log_api_call(
    logger: logging.Logger,
    api_name: str,
    endpoint: str,
    response_time: float,
    status_code: int = None,
    error: str = None
) -> None:
    """Log API call with structured information"""
    log_data = {
        "api_name": api_name,
        "endpoint": endpoint,
        "response_time_ms": round(response_time * 1000, 2),
        "status_code": status_code,
    }
    
    if error:
        log_data["error"] = error
        logger.error("API call failed", extra=log_data)
    else:
        logger.info("API call successful", extra=log_data)


def log_query_processing(
    logger: logging.Logger,
    query: str,
    processing_time: float,
    data_sources_used: list,
    success: bool = True,
    error: str = None
) -> None:
    """Log query processing with structured information"""
    log_data = {
        "query": query[:100] + "..." if len(query) > 100 else query,
        "processing_time_ms": round(processing_time * 1000, 2),
        "data_sources_used": data_sources_used,
        "success": success,
    }
    
    if error:
        log_data["error"] = error
        logger.error("Query processing failed", extra=log_data)
    else:
        logger.info("Query processed successfully", extra=log_data)
