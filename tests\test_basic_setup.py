"""
Basic Setup Tests
Tests to verify the foundational components are working correctly
"""
import pytest
import asyncio
from unittest.mock import Mock, patch
import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.config.settings import Settings
from src.config.logging_config import setup_logging, get_logger
from src.data_sources.base import BaseDataSource, DataSourceResponse, DataSourceType


class TestSettings:
    """Test configuration settings"""
    
    def test_settings_validation(self):
        """Test that settings validation works"""
        # This will use default values from .env.example
        with patch.dict(os.environ, {
            'OPENAI_API_KEY': 'test_key',
            'ALPHA_VANTAGE_API_KEY': 'test_key',
            'FINNHUB_API_KEY': 'test_key',
            'QDRANT_URL': 'https://test.qdrant.tech',
            'QDRANT_API_KEY': 'test_key'
        }):
            settings = Settings()
            
            assert settings.openai_api_key == 'test_key'
            assert settings.alpha_vantage_api_key == 'test_key'
            assert settings.finnhub_api_key == 'test_key'
            assert settings.qdrant_url == 'https://test.qdrant.tech'
            assert settings.qdrant_api_key == 'test_key'
            assert settings.openai_model == 'gpt-4o-mini'
            assert settings.embedding_model == 'text-embedding-3-small'
    
    def test_settings_validation_errors(self):
        """Test that settings validation catches errors"""
        with pytest.raises(ValueError):
            Settings(log_level="INVALID")
        
        with pytest.raises(ValueError):
            Settings(similarity_threshold=1.5)  # Should be <= 1.0


class TestLogging:
    """Test logging configuration"""
    
    def test_logging_setup(self):
        """Test that logging setup works"""
        setup_logging()
        logger = get_logger("test")
        
        assert logger is not None
        assert logger.name == "financial_chatbot.test"
    
    def test_logger_creation(self):
        """Test logger creation"""
        logger = get_logger("test_module")
        
        assert logger is not None
        assert "test_module" in logger.name


class MockDataSource(BaseDataSource):
    """Mock data source for testing"""
    
    async def get_stock_price(self, symbol: str):
        return DataSourceResponse(
            success=True,
            data={"symbol": symbol, "price": 100.0},
            source=self.name
        )
    
    async def get_company_fundamentals(self, symbol: str):
        return DataSourceResponse(
            success=True,
            data={"symbol": symbol, "name": "Test Company"},
            source=self.name
        )
    
    async def get_financial_statements(self, symbol: str, statement_type: str, period: str = "annual"):
        return DataSourceResponse(
            success=True,
            data={"symbol": symbol, "statement_type": statement_type},
            source=self.name
        )
    
    async def get_historical_data(self, symbol: str, start_date, end_date):
        return DataSourceResponse(
            success=True,
            data={"symbol": symbol, "data": []},
            source=self.name
        )


class TestBaseDataSource:
    """Test base data source functionality"""
    
    def test_data_source_creation(self):
        """Test data source creation"""
        ds = MockDataSource(
            name="Test Source",
            source_type=DataSourceType.PRIMARY
        )
        
        assert ds.name == "Test Source"
        assert ds.source_type == DataSourceType.PRIMARY
        assert ds.circuit_breaker is not None
        assert ds.rate_limit_info is not None
    
    @pytest.mark.asyncio
    async def test_data_source_methods(self):
        """Test data source methods"""
        ds = MockDataSource(
            name="Test Source",
            source_type=DataSourceType.PRIMARY
        )
        
        # Test stock price
        response = await ds.get_stock_price("AAPL")
        assert response.success
        assert response.data["symbol"] == "AAPL"
        assert response.source == "Test Source"
        
        # Test company fundamentals
        response = await ds.get_company_fundamentals("AAPL")
        assert response.success
        assert response.data["symbol"] == "AAPL"
        
        # Test financial statements
        response = await ds.get_financial_statements("AAPL", "income")
        assert response.success
        assert response.data["statement_type"] == "income"
    
    def test_cache_functionality(self):
        """Test caching functionality"""
        ds = MockDataSource(
            name="Test Source",
            source_type=DataSourceType.PRIMARY
        )
        
        # Test cache key generation
        cache_key = ds.get_cache_key("test_method", symbol="AAPL", period="annual")
        assert "Test Source" in cache_key
        assert "test_method" in cache_key
        assert "symbol:AAPL" in cache_key
        assert "period:annual" in cache_key
        
        # Test cache operations
        test_response = DataSourceResponse(
            success=True,
            data={"test": "data"},
            source="Test Source"
        )
        
        # Cache response
        ds.cache_response(cache_key, test_response)
        
        # Retrieve from cache
        cached = ds.get_cached_response(cache_key, ttl_seconds=3600)
        assert cached is not None
        assert cached.data["test"] == "data"
        assert cached.cached == True
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        ds = MockDataSource(
            name="Test Source",
            source_type=DataSourceType.PRIMARY
        )
        
        # Should be within limits initially
        assert ds._check_rate_limit() == True
        
        # Simulate hitting rate limit
        ds.rate_limit_info.current_minute_count = ds.rate_limit_info.requests_per_minute
        assert ds._check_rate_limit() == False
    
    def test_circuit_breaker(self):
        """Test circuit breaker functionality"""
        ds = MockDataSource(
            name="Test Source",
            source_type=DataSourceType.PRIMARY
        )
        
        # Should be closed initially
        assert ds.circuit_breaker.can_execute() == True
        
        # Simulate failures
        for _ in range(ds.circuit_breaker.failure_threshold):
            ds.circuit_breaker.record_failure()
        
        # Should be open now
        assert ds.circuit_breaker.can_execute() == False
        
        # Record success should close it
        ds.circuit_breaker.record_success()
        assert ds.circuit_breaker.can_execute() == True


class TestProjectStructure:
    """Test that project structure is correct"""
    
    def test_required_directories_exist(self):
        """Test that all required directories exist"""
        base_path = os.path.join(os.path.dirname(__file__), '..')
        
        required_dirs = [
            'src',
            'src/config',
            'src/data_sources',
            'src/vector_db',
            'src/query_processing',
            'src/api',
            'src/utils',
            'static',
            'tests'
        ]
        
        for dir_path in required_dirs:
            full_path = os.path.join(base_path, dir_path)
            assert os.path.exists(full_path), f"Directory {dir_path} does not exist"
    
    def test_required_files_exist(self):
        """Test that required files exist"""
        base_path = os.path.join(os.path.dirname(__file__), '..')
        
        required_files = [
            'requirements.txt',
            '.env.example',
            '.gitignore',
            'README.md',
            'src/__init__.py',
            'src/config/settings.py',
            'src/config/logging_config.py',
            'src/data_sources/base.py',
            'src/api/main.py',
            'src/api/models.py',
            'static/index.html',
            'static/style.css',
            'static/script.js'
        ]
        
        for file_path in required_files:
            full_path = os.path.join(base_path, file_path)
            assert os.path.exists(full_path), f"File {file_path} does not exist"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
