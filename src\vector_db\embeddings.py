"""
OpenAI Embeddings Client
Handles text embedding generation using OpenAI's text-embedding-3-small model
Includes rate limiting and batch processing for efficiency
"""
import asyncio
import openai
from typing import List, Dict, Any, Optional
from datetime import datetime
import time
import math

from ..config.settings import get_settings
from ..config.logging_config import get_logger


class OpenAIEmbeddingsClient:
    """OpenAI embeddings client with rate limiting and batch processing"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("embeddings")
        
        # Initialize OpenAI client
        openai.api_key = self.settings.openai_api_key
        self.client = openai.AsyncOpenAI(api_key=self.settings.openai_api_key)
        
        # Rate limiting
        self.requests_per_minute = self.settings.openai_requests_per_minute
        self.last_request_time = 0
        self.request_count = 0
        self.request_times = []
        
        # Batch processing settings
        self.max_batch_size = 100  # OpenAI allows up to 2048 inputs per request
        self.max_tokens_per_request = 8000  # Conservative limit
    
    async def _rate_limit_check(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Remove requests older than 1 minute
        self.request_times = [t for t in self.request_times if current_time - t < 60]
        
        # Check if we're at the rate limit
        if len(self.request_times) >= self.requests_per_minute:
            sleep_time = 60 - (current_time - self.request_times[0])
            if sleep_time > 0:
                self.logger.info(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
        
        # Record this request
        self.request_times.append(current_time)
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)"""
        # Rough estimation: 1 token ≈ 4 characters for English text
        return len(text) // 4
    
    def _create_batches(self, texts: List[str]) -> List[List[str]]:
        """Create batches of texts that fit within API limits"""
        batches = []
        current_batch = []
        current_tokens = 0
        
        for text in texts:
            text_tokens = self._estimate_tokens(text)
            
            # Check if adding this text would exceed limits
            if (len(current_batch) >= self.max_batch_size or 
                current_tokens + text_tokens > self.max_tokens_per_request):
                
                if current_batch:
                    batches.append(current_batch)
                    current_batch = []
                    current_tokens = 0
            
            current_batch.append(text)
            current_tokens += text_tokens
        
        # Add the last batch if it has content
        if current_batch:
            batches.append(current_batch)
        
        return batches
    
    async def generate_embeddings(
        self, 
        texts: List[str],
        model: Optional[str] = None
    ) -> List[List[float]]:
        """Generate embeddings for a list of texts"""
        if not texts:
            return []
        
        model = model or self.settings.embedding_model
        
        try:
            # Create batches to handle large lists efficiently
            batches = self._create_batches(texts)
            all_embeddings = []
            
            self.logger.info(f"Generating embeddings for {len(texts)} texts in {len(batches)} batches")
            
            for i, batch in enumerate(batches):
                await self._rate_limit_check()
                
                start_time = time.time()
                
                # Make API request
                response = await self.client.embeddings.create(
                    model=model,
                    input=batch
                )
                
                # Extract embeddings from response
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
                
                response_time = time.time() - start_time
                
                self.logger.info(
                    f"Generated embeddings for batch {i+1}/{len(batches)} "
                    f"({len(batch)} texts) in {response_time:.2f}s"
                )
            
            self.logger.info(f"Successfully generated {len(all_embeddings)} embeddings")
            return all_embeddings
            
        except Exception as e:
            self.logger.error(f"Error generating embeddings: {str(e)}")
            raise
    
    async def generate_single_embedding(
        self, 
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """Generate embedding for a single text"""
        embeddings = await self.generate_embeddings([text], model)
        return embeddings[0] if embeddings else []
    
    async def generate_query_embedding(self, query: str) -> List[float]:
        """Generate embedding for a search query"""
        return await self.generate_single_embedding(query)
    
    def chunk_text(
        self, 
        text: str, 
        chunk_size: Optional[int] = None,
        overlap: Optional[int] = None
    ) -> List[str]:
        """Split text into chunks for embedding"""
        chunk_size = chunk_size or self.settings.chunk_size
        overlap = overlap or self.settings.chunk_overlap
        
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundaries
            if end < len(text):
                # Look for sentence endings within the last 100 characters
                sentence_end = text.rfind('.', start + chunk_size - 100, end)
                if sentence_end > start:
                    end = sentence_end + 1
                else:
                    # Look for other punctuation
                    for punct in ['!', '?', '\n\n', '\n']:
                        punct_pos = text.rfind(punct, start + chunk_size - 100, end)
                        if punct_pos > start:
                            end = punct_pos + len(punct)
                            break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    async def process_document(
        self, 
        text: str, 
        metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process a document: chunk text and generate embeddings"""
        try:
            start_time = time.time()
            
            # Chunk the text
            chunks = self.chunk_text(text)
            self.logger.info(f"Split document into {len(chunks)} chunks")
            
            # Generate embeddings for all chunks
            embeddings = await self.generate_embeddings(chunks)
            
            processing_time = time.time() - start_time
            
            result = {
                "chunks": chunks,
                "embeddings": embeddings,
                "metadata": metadata,
                "processing_stats": {
                    "chunk_count": len(chunks),
                    "embedding_count": len(embeddings),
                    "processing_time_seconds": processing_time,
                    "average_chunk_length": sum(len(chunk) for chunk in chunks) / len(chunks) if chunks else 0
                }
            }
            
            self.logger.info(
                f"Processed document: {len(chunks)} chunks, "
                f"{len(embeddings)} embeddings in {processing_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing document: {str(e)}")
            raise
    
    async def health_check(self) -> bool:
        """Check if OpenAI embeddings service is healthy"""
        try:
            # Test with a simple embedding request
            test_embedding = await self.generate_single_embedding("test")
            return len(test_embedding) == self.settings.embedding_dimension
        except Exception as e:
            self.logger.error(f"OpenAI embeddings health check failed: {str(e)}")
            return False
    
    def get_embedding_stats(self) -> Dict[str, Any]:
        """Get statistics about embedding usage"""
        current_time = time.time()
        recent_requests = [t for t in self.request_times if current_time - t < 60]
        
        return {
            "requests_last_minute": len(recent_requests),
            "rate_limit": self.requests_per_minute,
            "model": self.settings.embedding_model,
            "embedding_dimension": self.settings.embedding_dimension,
            "chunk_size": self.settings.chunk_size,
            "chunk_overlap": self.settings.chunk_overlap
        }
