"""
Finnhub API Client
Provides access to real-time market data, earnings, and financial news
Rate limit: 60 calls per minute (free tier)
"""
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import time

from .base import BaseDataSource, DataSourceResponse, DataSourceType, RateLimitInfo
from ..config.logging_config import get_logger


class FinnhubClient(BaseDataSource):
    """Finnhub API client with rate limiting and error handling"""
    
    BASE_URL = "https://finnhub.io/api/v1"
    
    def __init__(self, api_key: str):
        rate_limit_info = RateLimitInfo(
            requests_per_minute=60,
            requests_per_day=1000  # Generous daily limit
        )
        
        super().__init__(
            name="Finnhub",
            source_type=DataSourceType.SECONDARY,
            api_key=api_key,
            rate_limit_info=rate_limit_info
        )
        
        self.logger = get_logger("finnhub")
    
    async def _make_request(self, endpoint: str, params: Dict[str, str] = None) -> Dict[str, Any]:
        """Make HTTP request to Finnhub API"""
        if params is None:
            params = {}
        
        params["token"] = self.api_key
        url = f"{self.BASE_URL}/{endpoint}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 429:
                    raise Exception("Rate limit exceeded")
                
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {await response.text()}")
                
                data = await response.json()
                
                # Check for API errors
                if isinstance(data, dict) and "error" in data:
                    raise Exception(f"API Error: {data['error']}")
                
                return data
    
    async def get_stock_price(self, symbol: str) -> DataSourceResponse:
        """Get current stock price using quote endpoint"""
        cache_key = self.get_cache_key("stock_price", symbol=symbol)
        
        # Check cache first (1 minute TTL for real-time data)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=60)
        if cached_response:
            return cached_response
        
        async def _fetch_price():
            data = await self._make_request("quote", {"symbol": symbol})
            
            if not data or data.get("c") is None:
                raise Exception(f"No price data found for symbol {symbol}")
            
            # Parse Finnhub quote response
            price_data = {
                "symbol": symbol,
                "price": float(data.get("c", 0)),  # Current price
                "change": float(data.get("d", 0)),  # Change
                "change_percent": float(data.get("dp", 0)),  # Change percent
                "high": float(data.get("h", 0)),  # High price of the day
                "low": float(data.get("l", 0)),  # Low price of the day
                "open": float(data.get("o", 0)),  # Open price of the day
                "previous_close": float(data.get("pc", 0)),  # Previous close
                "timestamp": datetime.fromtimestamp(data.get("t", time.time()))
            }
            
            return DataSourceResponse(
                success=True,
                data=price_data,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_price)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_company_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get company profile and basic metrics"""
        cache_key = self.get_cache_key("company_fundamentals", symbol=symbol)
        
        # Check cache first (24 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=86400)
        if cached_response:
            return cached_response
        
        async def _fetch_fundamentals():
            # Get company profile
            profile_data = await self._make_request("stock/profile2", {"symbol": symbol})
            
            if not profile_data:
                raise Exception(f"No company profile found for symbol {symbol}")
            
            # Get basic financials
            try:
                metrics_data = await self._make_request("stock/metric", {"symbol": symbol, "metric": "all"})
                metrics = metrics_data.get("metric", {}) if metrics_data else {}
            except Exception as e:
                self.logger.warning(f"Could not fetch metrics for {symbol}: {e}")
                metrics = {}
            
            # Combine profile and metrics
            fundamentals = {
                "symbol": symbol,
                "name": profile_data.get("name"),
                "country": profile_data.get("country"),
                "currency": profile_data.get("currency"),
                "exchange": profile_data.get("exchange"),
                "ipo": profile_data.get("ipo"),
                "market_cap": profile_data.get("marketCapitalization"),
                "shares_outstanding": profile_data.get("shareOutstanding"),
                "industry": profile_data.get("finnhubIndustry"),
                "website": profile_data.get("weburl"),
                "logo": profile_data.get("logo"),
                "phone": profile_data.get("phone"),
                
                # Financial metrics (if available)
                "pe_ratio": metrics.get("peBasicExclExtraTTM"),
                "pe_ratio_forward": metrics.get("peNormalizedAnnual"),
                "price_to_book": metrics.get("pbAnnual"),
                "price_to_sales": metrics.get("psAnnual"),
                "ev_to_ebitda": metrics.get("evToEbitdaTTM"),
                "profit_margin": metrics.get("netProfitMarginTTM"),
                "operating_margin": metrics.get("operatingMarginTTM"),
                "return_on_equity": metrics.get("roeTTM"),
                "return_on_assets": metrics.get("roaTTM"),
                "debt_to_equity": metrics.get("totalDebt/totalEquityAnnual"),
                "current_ratio": metrics.get("currentRatioAnnual"),
                "quick_ratio": metrics.get("quickRatioAnnual"),
                "beta": metrics.get("beta")
            }
            
            return DataSourceResponse(
                success=True,
                data=fundamentals,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_fundamentals)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_financial_statements(
        self, 
        symbol: str, 
        statement_type: str,
        period: str = "annual"
    ) -> DataSourceResponse:
        """Get financial statements - limited in Finnhub free tier"""
        cache_key = self.get_cache_key(
            "financial_statements", 
            symbol=symbol, 
            statement_type=statement_type,
            period=period
        )
        
        # Check cache first (24 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=86400)
        if cached_response:
            return cached_response
        
        # Map statement types to Finnhub endpoints
        endpoint_map = {
            "income": "financials",
            "balance": "financials-reported",
            "cash_flow": "financials-reported"
        }
        
        if statement_type not in endpoint_map:
            return DataSourceResponse(
                success=False,
                error=f"Statement type {statement_type} not supported by Finnhub",
                source=self.name
            )
        
        async def _fetch_statements():
            params = {
                "symbol": symbol,
                "freq": "annual" if period == "annual" else "quarterly"
            }
            
            data = await self._make_request(f"stock/{endpoint_map[statement_type]}", params)
            
            if not data or "financials" not in data:
                raise Exception(f"No {statement_type} data found for symbol {symbol}")
            
            financials = data["financials"]
            if not financials:
                raise Exception(f"No {period} {statement_type} reports found")
            
            # Structure the financial statement data
            statements = {
                "symbol": symbol,
                "statement_type": statement_type,
                "period": period,
                "reports": financials[:5],  # Last 5 periods
                "metadata": {
                    "currency": data.get("currency", "USD"),
                    "last_updated": datetime.utcnow().isoformat()
                }
            }
            
            return DataSourceResponse(
                success=True,
                data=statements,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_statements)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> DataSourceResponse:
        """Get historical daily price data"""
        cache_key = self.get_cache_key(
            "historical_data",
            symbol=symbol,
            start_date=start_date.date().isoformat(),
            end_date=end_date.date().isoformat()
        )
        
        # Check cache first (1 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=3600)
        if cached_response:
            return cached_response
        
        async def _fetch_historical():
            params = {
                "symbol": symbol,
                "resolution": "D",  # Daily resolution
                "from": int(start_date.timestamp()),
                "to": int(end_date.timestamp())
            }
            
            data = await self._make_request("stock/candle", params)
            
            if not data or data.get("s") != "ok":
                raise Exception(f"No historical data found for symbol {symbol}")
            
            # Convert arrays to list of dictionaries
            historical_data = []
            timestamps = data.get("t", [])
            opens = data.get("o", [])
            highs = data.get("h", [])
            lows = data.get("l", [])
            closes = data.get("c", [])
            volumes = data.get("v", [])
            
            for i in range(len(timestamps)):
                historical_data.append({
                    "date": datetime.fromtimestamp(timestamps[i]).strftime("%Y-%m-%d"),
                    "open": opens[i],
                    "high": highs[i],
                    "low": lows[i],
                    "close": closes[i],
                    "volume": volumes[i]
                })
            
            # Sort by date (newest first)
            historical_data.sort(key=lambda x: x["date"], reverse=True)
            
            result = {
                "symbol": symbol,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "data": historical_data
            }
            
            return DataSourceResponse(
                success=True,
                data=result,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_historical)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_company_news(self, symbol: str, days: int = 7) -> DataSourceResponse:
        """Get recent company news"""
        cache_key = self.get_cache_key("company_news", symbol=symbol, days=days)
        
        # Check cache first (30 minute TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=1800)
        if cached_response:
            return cached_response
        
        async def _fetch_news():
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            params = {
                "symbol": symbol,
                "from": start_date.strftime("%Y-%m-%d"),
                "to": end_date.strftime("%Y-%m-%d")
            }
            
            data = await self._make_request("company-news", params)
            
            if not data:
                raise Exception(f"No news found for symbol {symbol}")
            
            # Structure news data
            news_data = {
                "symbol": symbol,
                "articles": data[:10],  # Limit to 10 most recent articles
                "date_range": {
                    "from": start_date.isoformat(),
                    "to": end_date.isoformat()
                }
            }
            
            return DataSourceResponse(
                success=True,
                data=news_data,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_news)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
