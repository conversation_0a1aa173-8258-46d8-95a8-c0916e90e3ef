"""
Query Parser with Entity Extraction and Intent Classification
Uses regex patterns and OpenAI function calling for intelligent query analysis
"""
import re
import asyncio
import openai
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from ..config.settings import get_settings
from ..config.logging_config import get_logger


class QueryType(Enum):
    """Types of financial queries"""
    STOCK_PRICE = "stock_price"
    COMPANY_FUNDAMENTALS = "company_fundamentals"
    FINANCIAL_STATEMENTS = "financial_statements"
    COMPARISON = "comparison"
    TREND_ANALYSIS = "trend_analysis"
    CALCULATION = "calculation"
    NEWS = "news"
    GENERAL = "general"


class StatementType(Enum):
    """Types of financial statements"""
    INCOME = "income"
    BALANCE = "balance"
    CASH_FLOW = "cash_flow"


class TimeFrame(Enum):
    """Time frame for queries"""
    CURRENT = "current"
    QUARTERLY = "quarterly"
    ANNUAL = "annual"
    HISTORICAL = "historical"


@dataclass
class ExtractedEntity:
    """Extracted entity from query"""
    type: str
    value: str
    confidence: float
    start_pos: int
    end_pos: int


@dataclass
class ParsedQuery:
    """Parsed query with extracted information"""
    original_query: str
    query_type: QueryType
    entities: List[ExtractedEntity]
    symbols: List[str]
    metrics: List[str]
    time_frame: Optional[TimeFrame]
    statement_type: Optional[StatementType]
    date_range: Optional[Tuple[datetime, datetime]]
    comparison_symbols: List[str]
    confidence: float
    processing_notes: List[str]


class QueryParser:
    """Advanced query parser with entity extraction and intent classification"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("query_parser")
        self.client = openai.AsyncOpenAI(api_key=self.settings.openai_api_key)
        
        # Compile regex patterns for entity extraction
        self._compile_patterns()
        
        # Financial metrics mapping
        self.financial_metrics = {
            'revenue': ['revenue', 'sales', 'income', 'turnover'],
            'profit': ['profit', 'earnings', 'net income', 'bottom line'],
            'margin': ['margin', 'profit margin', 'gross margin', 'operating margin'],
            'cash_flow': ['cash flow', 'cashflow', 'operating cash flow', 'free cash flow'],
            'debt': ['debt', 'liabilities', 'debt to equity', 'leverage'],
            'assets': ['assets', 'total assets', 'current assets'],
            'equity': ['equity', 'shareholders equity', 'book value'],
            'eps': ['eps', 'earnings per share', 'earnings/share'],
            'pe_ratio': ['pe ratio', 'p/e ratio', 'price to earnings'],
            'market_cap': ['market cap', 'market capitalization', 'market value']
        }
    
    def _compile_patterns(self):
        """Compile regex patterns for entity extraction"""
        # Stock symbol patterns
        self.symbol_patterns = [
            re.compile(r'\b([A-Z]{1,5})\b(?:\s+(?:stock|shares?|equity))?', re.IGNORECASE),
            re.compile(r'\b(?:ticker|symbol):\s*([A-Z]{1,5})\b', re.IGNORECASE),
            re.compile(r'\$([A-Z]{1,5})\b', re.IGNORECASE)
        ]
        
        # Company name patterns (common companies)
        self.company_patterns = {
            'AAPL': ['apple', 'apple inc', 'apple computer'],
            'MSFT': ['microsoft', 'microsoft corp', 'microsoft corporation'],
            'GOOGL': ['google', 'alphabet', 'alphabet inc'],
            'AMZN': ['amazon', 'amazon.com', 'amazon inc'],
            'TSLA': ['tesla', 'tesla inc', 'tesla motors'],
            'META': ['meta', 'facebook', 'meta platforms'],
            'NVDA': ['nvidia', 'nvidia corp', 'nvidia corporation'],
            'NFLX': ['netflix', 'netflix inc'],
            'CRM': ['salesforce', 'salesforce.com'],
            'ORCL': ['oracle', 'oracle corp', 'oracle corporation']
        }
        
        # Date patterns
        self.date_patterns = [
            re.compile(r'\b(Q[1-4])\s+(\d{4})\b', re.IGNORECASE),  # Q1 2023
            re.compile(r'\b(\d{4})\s+Q([1-4])\b', re.IGNORECASE),  # 2023 Q1
            re.compile(r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b'),        # MM/DD/YYYY
            re.compile(r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b'),        # YYYY-MM-DD
            re.compile(r'\b(last|past)\s+(\d+)\s+(years?|quarters?|months?)\b', re.IGNORECASE),
            re.compile(r'\b(\d{4})\b'),  # Just year
        ]
        
        # Financial statement patterns
        self.statement_patterns = {
            StatementType.INCOME: ['income statement', 'profit and loss', 'p&l', 'earnings'],
            StatementType.BALANCE: ['balance sheet', 'financial position', 'assets and liabilities'],
            StatementType.CASH_FLOW: ['cash flow statement', 'cash flows', 'operating cash flow']
        }
        
        # Query type patterns
        self.query_type_patterns = {
            QueryType.STOCK_PRICE: [
                r'\b(?:current|latest|today\'?s?)\s+(?:stock\s+)?price\b',
                r'\bstock\s+price\b',
                r'\bshare\s+price\b',
                r'\bquote\b'
            ],
            QueryType.COMPARISON: [
                r'\bcompare\b',
                r'\bvs?\b',
                r'\bversus\b',
                r'\bdifference\s+between\b',
                r'\bwhich\s+is\s+better\b'
            ],
            QueryType.TREND_ANALYSIS: [
                r'\btrend\b',
                r'\bover\s+time\b',
                r'\bhistorical\b',
                r'\bpast\s+\d+\s+years?\b',
                r'\bgrowth\b',
                r'\bperformance\b'
            ],
            QueryType.FINANCIAL_STATEMENTS: [
                r'\bfinancial\s+statements?\b',
                r'\bincome\s+statement\b',
                r'\bbalance\s+sheet\b',
                r'\bcash\s+flow\b'
            ]
        }
    
    async def parse_query(self, query: str) -> ParsedQuery:
        """Parse query and extract entities, intent, and structure"""
        self.logger.info(f"Parsing query: {query[:100]}...")
        
        try:
            # Extract entities using regex patterns
            entities = await self._extract_entities(query)
            
            # Extract specific information
            symbols = self._extract_symbols(query, entities)
            metrics = self._extract_metrics(query)
            time_frame = self._extract_time_frame(query)
            statement_type = self._extract_statement_type(query)
            date_range = self._extract_date_range(query)
            
            # Classify query type using patterns and OpenAI
            query_type = await self._classify_query_type(query, entities)
            
            # Handle comparison queries
            comparison_symbols = []
            if query_type == QueryType.COMPARISON:
                comparison_symbols = symbols if len(symbols) > 1 else []
            
            # Calculate confidence score
            confidence = self._calculate_confidence(query, entities, symbols, metrics)
            
            # Generate processing notes
            processing_notes = self._generate_processing_notes(query, entities, symbols)
            
            parsed_query = ParsedQuery(
                original_query=query,
                query_type=query_type,
                entities=entities,
                symbols=symbols,
                metrics=metrics,
                time_frame=time_frame,
                statement_type=statement_type,
                date_range=date_range,
                comparison_symbols=comparison_symbols,
                confidence=confidence,
                processing_notes=processing_notes
            )
            
            self.logger.info(
                f"Query parsed successfully: type={query_type.value}, "
                f"symbols={symbols}, confidence={confidence:.2f}"
            )
            
            return parsed_query
            
        except Exception as e:
            self.logger.error(f"Error parsing query: {str(e)}")
            # Return basic parsed query on error
            return ParsedQuery(
                original_query=query,
                query_type=QueryType.GENERAL,
                entities=[],
                symbols=[],
                metrics=[],
                time_frame=None,
                statement_type=None,
                date_range=None,
                comparison_symbols=[],
                confidence=0.1,
                processing_notes=[f"Error during parsing: {str(e)}"]
            )
    
    async def _extract_entities(self, query: str) -> List[ExtractedEntity]:
        """Extract entities using regex patterns and OpenAI function calling"""
        entities = []
        
        # Extract stock symbols
        for pattern in self.symbol_patterns:
            for match in pattern.finditer(query):
                symbol = match.group(1).upper()
                if len(symbol) <= 5:  # Valid stock symbol length
                    entities.append(ExtractedEntity(
                        type="stock_symbol",
                        value=symbol,
                        confidence=0.8,
                        start_pos=match.start(),
                        end_pos=match.end()
                    ))
        
        # Extract company names and map to symbols
        query_lower = query.lower()
        for symbol, names in self.company_patterns.items():
            for name in names:
                if name in query_lower:
                    start_pos = query_lower.find(name)
                    entities.append(ExtractedEntity(
                        type="company_name",
                        value=symbol,
                        confidence=0.9,
                        start_pos=start_pos,
                        end_pos=start_pos + len(name)
                    ))
        
        # Use OpenAI for additional entity extraction
        try:
            openai_entities = await self._extract_entities_with_openai(query)
            entities.extend(openai_entities)
        except Exception as e:
            self.logger.warning(f"OpenAI entity extraction failed: {str(e)}")
        
        # Remove duplicates and sort by confidence
        entities = self._deduplicate_entities(entities)
        entities.sort(key=lambda x: x.confidence, reverse=True)
        
        return entities
    
    async def _extract_entities_with_openai(self, query: str) -> List[ExtractedEntity]:
        """Use OpenAI function calling for entity extraction"""
        function_schema = {
            "name": "extract_financial_entities",
            "description": "Extract financial entities from a query",
            "parameters": {
                "type": "object",
                "properties": {
                    "stock_symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Stock ticker symbols mentioned"
                    },
                    "company_names": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Company names mentioned"
                    },
                    "financial_metrics": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Financial metrics or KPIs mentioned"
                    },
                    "time_periods": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Time periods or dates mentioned"
                    }
                },
                "required": ["stock_symbols", "company_names", "financial_metrics", "time_periods"]
            }
        }
        
        response = await self.client.chat.completions.create(
            model=self.settings.openai_model,
            messages=[
                {
                    "role": "system",
                    "content": "You are a financial entity extraction expert. Extract relevant financial entities from user queries."
                },
                {
                    "role": "user",
                    "content": f"Extract financial entities from this query: {query}"
                }
            ],
            functions=[function_schema],
            function_call={"name": "extract_financial_entities"},
            max_tokens=500
        )
        
        entities = []
        if response.choices[0].message.function_call:
            try:
                function_args = json.loads(response.choices[0].message.function_call.arguments)
                
                # Process extracted entities
                for symbol in function_args.get("stock_symbols", []):
                    entities.append(ExtractedEntity(
                        type="stock_symbol",
                        value=symbol.upper(),
                        confidence=0.7,
                        start_pos=0,
                        end_pos=0
                    ))
                
                for metric in function_args.get("financial_metrics", []):
                    entities.append(ExtractedEntity(
                        type="financial_metric",
                        value=metric.lower(),
                        confidence=0.7,
                        start_pos=0,
                        end_pos=0
                    ))
                    
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse OpenAI function call response")
        
        return entities

    def _extract_symbols(self, query: str, entities: List[ExtractedEntity]) -> List[str]:
        """Extract stock symbols from query and entities"""
        symbols = set()

        # Get symbols from entities
        for entity in entities:
            if entity.type in ["stock_symbol", "company_name"]:
                symbols.add(entity.value.upper())

        # Validate symbols (basic validation)
        valid_symbols = []
        for symbol in symbols:
            if len(symbol) <= 5 and symbol.isalpha():
                valid_symbols.append(symbol)

        return list(set(valid_symbols))

    def _extract_metrics(self, query: str) -> List[str]:
        """Extract financial metrics from query"""
        query_lower = query.lower()
        metrics = []

        for metric_key, metric_terms in self.financial_metrics.items():
            for term in metric_terms:
                if term in query_lower:
                    metrics.append(metric_key)
                    break

        return list(set(metrics))

    def _extract_time_frame(self, query: str) -> Optional[TimeFrame]:
        """Extract time frame from query"""
        query_lower = query.lower()

        if any(word in query_lower for word in ['current', 'latest', 'today', 'now']):
            return TimeFrame.CURRENT
        elif any(word in query_lower for word in ['quarterly', 'quarter', 'q1', 'q2', 'q3', 'q4']):
            return TimeFrame.QUARTERLY
        elif any(word in query_lower for word in ['annual', 'yearly', 'year']):
            return TimeFrame.ANNUAL
        elif any(word in query_lower for word in ['historical', 'history', 'past', 'trend', 'over time']):
            return TimeFrame.HISTORICAL

        return None

    def _extract_statement_type(self, query: str) -> Optional[StatementType]:
        """Extract financial statement type from query"""
        query_lower = query.lower()

        for statement_type, patterns in self.statement_patterns.items():
            for pattern in patterns:
                if pattern in query_lower:
                    return statement_type

        return None

    def _extract_date_range(self, query: str) -> Optional[Tuple[datetime, datetime]]:
        """Extract date range from query"""
        for pattern in self.date_patterns:
            match = pattern.search(query)
            if match:
                try:
                    # Handle different date formats
                    if 'Q' in match.group(0):  # Quarter format
                        if match.group(1).startswith('Q'):
                            quarter = int(match.group(1)[1])
                            year = int(match.group(2))
                        else:
                            year = int(match.group(1))
                            quarter = int(match.group(2))

                        # Convert quarter to date range
                        start_month = (quarter - 1) * 3 + 1
                        start_date = datetime(year, start_month, 1)
                        if quarter == 4:
                            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
                        else:
                            end_date = datetime(year, start_month + 3, 1) - timedelta(days=1)

                        return (start_date, end_date)

                    elif len(match.groups()) == 1:  # Just year
                        year = int(match.group(1))
                        start_date = datetime(year, 1, 1)
                        end_date = datetime(year, 12, 31)
                        return (start_date, end_date)

                except ValueError:
                    continue

        return None

    async def _classify_query_type(self, query: str, entities: List[ExtractedEntity]) -> QueryType:
        """Classify query type using patterns and OpenAI"""
        query_lower = query.lower()

        # Check patterns first
        for query_type, patterns in self.query_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return query_type

        # Use OpenAI for classification if patterns don't match
        try:
            classification = await self._classify_with_openai(query)
            return classification
        except Exception as e:
            self.logger.warning(f"OpenAI classification failed: {str(e)}")
            return QueryType.GENERAL

    async def _classify_with_openai(self, query: str) -> QueryType:
        """Use OpenAI to classify query type"""
        function_schema = {
            "name": "classify_financial_query",
            "description": "Classify the type of financial query",
            "parameters": {
                "type": "object",
                "properties": {
                    "query_type": {
                        "type": "string",
                        "enum": [qt.value for qt in QueryType],
                        "description": "The type of financial query"
                    },
                    "confidence": {
                        "type": "number",
                        "description": "Confidence score between 0 and 1"
                    }
                },
                "required": ["query_type", "confidence"]
            }
        }

        response = await self.client.chat.completions.create(
            model=self.settings.openai_model,
            messages=[
                {
                    "role": "system",
                    "content": "You are a financial query classifier. Classify queries into specific types based on their intent."
                },
                {
                    "role": "user",
                    "content": f"Classify this financial query: {query}"
                }
            ],
            functions=[function_schema],
            function_call={"name": "classify_financial_query"},
            max_tokens=200
        )

        if response.choices[0].message.function_call:
            try:
                function_args = json.loads(response.choices[0].message.function_call.arguments)
                query_type_str = function_args.get("query_type", "general")
                return QueryType(query_type_str)
            except (json.JSONDecodeError, ValueError):
                pass

        return QueryType.GENERAL

    def _calculate_confidence(self, query: str, entities: List[ExtractedEntity],
                            symbols: List[str], metrics: List[str]) -> float:
        """Calculate confidence score for the parsed query"""
        confidence = 0.0

        # Base confidence
        confidence += 0.2

        # Entity confidence
        if entities:
            avg_entity_confidence = sum(e.confidence for e in entities) / len(entities)
            confidence += avg_entity_confidence * 0.3

        # Symbol confidence
        if symbols:
            confidence += 0.3

        # Metrics confidence
        if metrics:
            confidence += 0.2

        # Query length and structure
        if len(query.split()) >= 3:
            confidence += 0.1

        return min(confidence, 1.0)

    def _generate_processing_notes(self, query: str, entities: List[ExtractedEntity],
                                 symbols: List[str]) -> List[str]:
        """Generate processing notes for debugging and transparency"""
        notes = []

        if not symbols:
            notes.append("No stock symbols detected - may need manual symbol identification")

        if len(entities) == 0:
            notes.append("No entities extracted - query may be too general")

        if len(query.split()) < 3:
            notes.append("Short query - may need clarification")

        symbol_entities = [e for e in entities if e.type in ["stock_symbol", "company_name"]]
        if len(symbol_entities) > 3:
            notes.append("Multiple symbols detected - may be comparison query")

        return notes

    def _deduplicate_entities(self, entities: List[ExtractedEntity]) -> List[ExtractedEntity]:
        """Remove duplicate entities"""
        seen = set()
        unique_entities = []

        for entity in entities:
            key = (entity.type, entity.value.upper())
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)

        return unique_entities
