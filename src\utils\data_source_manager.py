"""
Data Source Manager
Unified interface for all financial data sources with automatic failover and caching
"""
import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

from ..data_sources.base import BaseDataSource, DataSourceResponse
from ..data_sources.alpha_vantage import AlphaVantageClient
from ..data_sources.finnhub import FinnhubClient
from ..data_sources.yfinance_client import YFinanceClient
from ..query_processing.router import DataSourceRouter, RoutingStrategy
from ..query_processing.parser import ParsedQuery, QueryType
from ..config.settings import get_settings
from ..config.logging_config import get_logger


class DataSourceManager:
    """Unified manager for all financial data sources"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("data_source_manager")
        self.router = DataSourceRouter()
        
        # Initialize data sources
        self.sources: Dict[str, BaseDataSource] = {}
        self._initialize_sources()
        
        # Cache for responses
        self.cache: Dict[str, tuple] = {}  # key -> (response, timestamp)
        self.cache_ttl = {
            QueryType.STOCK_PRICE: 60,  # 1 minute
            QueryType.COMPANY_FUNDAMENTALS: 3600,  # 1 hour
            QueryType.FINANCIAL_STATEMENTS: 86400,  # 24 hours
            QueryType.TREND_ANALYSIS: 3600,  # 1 hour
            QueryType.COMPARISON: 1800,  # 30 minutes
            QueryType.NEWS: 900,  # 15 minutes
            QueryType.GENERAL: 1800  # 30 minutes
        }
    
    def _initialize_sources(self):
        """Initialize all data source clients"""
        try:
            # Alpha Vantage
            self.sources["Alpha Vantage"] = AlphaVantageClient(
                self.settings.alpha_vantage_api_key
            )
            self.logger.info("Alpha Vantage client initialized")
            
            # Finnhub
            self.sources["Finnhub"] = FinnhubClient(
                self.settings.finnhub_api_key
            )
            self.logger.info("Finnhub client initialized")
            
            # Yahoo Finance
            self.sources["Yahoo Finance"] = YFinanceClient()
            self.logger.info("Yahoo Finance client initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing data sources: {str(e)}")
            raise
    
    async def get_stock_price(
        self, 
        symbol: str, 
        strategy: RoutingStrategy = RoutingStrategy.FASTEST
    ) -> DataSourceResponse:
        """Get current stock price with automatic source selection"""
        
        # Create a simple parsed query for routing
        from ..query_processing.parser import ParsedQuery, ExtractedEntity
        parsed_query = ParsedQuery(
            original_query=f"stock price for {symbol}",
            query_type=QueryType.STOCK_PRICE,
            entities=[],
            symbols=[symbol],
            metrics=[],
            time_frame=None,
            statement_type=None,
            date_range=None,
            comparison_symbols=[],
            confidence=0.8,
            processing_notes=[]
        )
        
        return await self._execute_with_routing(
            parsed_query, 
            lambda source: source.get_stock_price(symbol),
            strategy
        )
    
    async def get_company_fundamentals(
        self, 
        symbol: str,
        strategy: RoutingStrategy = RoutingStrategy.BEST_COVERAGE
    ) -> DataSourceResponse:
        """Get company fundamentals with automatic source selection"""
        
        from ..query_processing.parser import ParsedQuery
        parsed_query = ParsedQuery(
            original_query=f"fundamentals for {symbol}",
            query_type=QueryType.COMPANY_FUNDAMENTALS,
            entities=[],
            symbols=[symbol],
            metrics=[],
            time_frame=None,
            statement_type=None,
            date_range=None,
            comparison_symbols=[],
            confidence=0.8,
            processing_notes=[]
        )
        
        return await self._execute_with_routing(
            parsed_query,
            lambda source: source.get_company_fundamentals(symbol),
            strategy
        )
    
    async def get_financial_statements(
        self, 
        symbol: str, 
        statement_type: str,
        period: str = "annual",
        strategy: RoutingStrategy = RoutingStrategy.BEST_COVERAGE
    ) -> DataSourceResponse:
        """Get financial statements with automatic source selection"""
        
        from ..query_processing.parser import ParsedQuery, StatementType
        try:
            stmt_type = StatementType(statement_type)
        except ValueError:
            stmt_type = None
        
        parsed_query = ParsedQuery(
            original_query=f"{statement_type} statement for {symbol}",
            query_type=QueryType.FINANCIAL_STATEMENTS,
            entities=[],
            symbols=[symbol],
            metrics=[],
            time_frame=None,
            statement_type=stmt_type,
            date_range=None,
            comparison_symbols=[],
            confidence=0.8,
            processing_notes=[]
        )
        
        return await self._execute_with_routing(
            parsed_query,
            lambda source: source.get_financial_statements(symbol, statement_type, period),
            strategy
        )
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        strategy: RoutingStrategy = RoutingStrategy.BEST_COVERAGE
    ) -> DataSourceResponse:
        """Get historical data with automatic source selection"""
        
        from ..query_processing.parser import ParsedQuery
        parsed_query = ParsedQuery(
            original_query=f"historical data for {symbol}",
            query_type=QueryType.TREND_ANALYSIS,
            entities=[],
            symbols=[symbol],
            metrics=[],
            time_frame=None,
            statement_type=None,
            date_range=(start_date, end_date),
            comparison_symbols=[],
            confidence=0.8,
            processing_notes=[]
        )
        
        return await self._execute_with_routing(
            parsed_query,
            lambda source: source.get_historical_data(symbol, start_date, end_date),
            strategy
        )
    
    async def _execute_with_routing(
        self,
        parsed_query: ParsedQuery,
        operation_func,
        strategy: RoutingStrategy
    ) -> DataSourceResponse:
        """Execute operation with intelligent routing and failover"""
        
        # Check cache first
        cache_key = self._generate_cache_key(parsed_query, operation_func.__name__)
        cached_response = self._get_cached_response(cache_key, parsed_query.query_type)
        if cached_response:
            self.logger.info(f"Cache hit for {cache_key}")
            return cached_response
        
        # Get routing decision
        routing_decision = await self.router.route_query(parsed_query, strategy)
        
        # Try primary source
        primary_source = routing_decision.primary_source
        if primary_source in self.sources:
            response = await self._try_source(
                self.sources[primary_source], 
                operation_func, 
                primary_source
            )
            
            if response.success:
                self._cache_response(cache_key, response, parsed_query.query_type)
                return response
        
        # Try fallback sources
        for fallback_source in routing_decision.fallback_sources:
            if fallback_source in self.sources:
                self.logger.info(f"Trying fallback source: {fallback_source}")
                response = await self._try_source(
                    self.sources[fallback_source], 
                    operation_func, 
                    fallback_source
                )
                
                if response.success:
                    self._cache_response(cache_key, response, parsed_query.query_type)
                    return response
        
        # All sources failed
        return DataSourceResponse(
            success=False,
            error="All data sources failed",
            source="DataSourceManager",
            timestamp=datetime.utcnow()
        )
    
    async def _try_source(
        self, 
        source: BaseDataSource, 
        operation_func, 
        source_name: str
    ) -> DataSourceResponse:
        """Try executing operation on a specific source"""
        start_time = time.time()
        
        try:
            response = await operation_func(source)
            response_time = (time.time() - start_time) * 1000
            
            # Update router health
            self.router.update_source_health(source_name, response.success, response_time)
            
            if response.success:
                self.logger.info(f"Success from {source_name} in {response_time:.0f}ms")
            else:
                self.logger.warning(f"Failed response from {source_name}: {response.error}")
            
            return response
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            self.router.update_source_health(source_name, False, response_time)
            
            self.logger.error(f"Exception from {source_name}: {str(e)}")
            
            return DataSourceResponse(
                success=False,
                error=f"Exception from {source_name}: {str(e)}",
                source=source_name,
                timestamp=datetime.utcnow()
            )
    
    def _generate_cache_key(self, parsed_query: ParsedQuery, operation: str) -> str:
        """Generate cache key for the query"""
        key_parts = [
            operation,
            parsed_query.query_type.value,
            ":".join(sorted(parsed_query.symbols)),
            ":".join(sorted(parsed_query.metrics))
        ]
        
        if parsed_query.statement_type:
            key_parts.append(parsed_query.statement_type.value)
        
        if parsed_query.date_range:
            start, end = parsed_query.date_range
            key_parts.append(f"{start.date()}:{end.date()}")
        
        return ":".join(key_parts)
    
    def _get_cached_response(self, cache_key: str, query_type: QueryType) -> Optional[DataSourceResponse]:
        """Get cached response if available and not expired"""
        if cache_key not in self.cache:
            return None
        
        response, timestamp = self.cache[cache_key]
        ttl = self.cache_ttl.get(query_type, 1800)  # Default 30 minutes
        
        if (datetime.utcnow() - timestamp).seconds < ttl:
            response.cached = True
            return response
        else:
            # Remove expired cache entry
            del self.cache[cache_key]
            return None
    
    def _cache_response(self, cache_key: str, response: DataSourceResponse, query_type: QueryType):
        """Cache response data"""
        self.cache[cache_key] = (response, datetime.utcnow())
        
        # Clean up old cache entries (keep last 100)
        if len(self.cache) > 100:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
    
    def get_source_status(self) -> Dict[str, Any]:
        """Get status of all data sources"""
        return self.router.get_source_status()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_entries = len(self.cache)
        cache_by_type = {}
        
        for key, (response, timestamp) in self.cache.items():
            query_type = key.split(":")[1] if ":" in key else "unknown"
            cache_by_type[query_type] = cache_by_type.get(query_type, 0) + 1
        
        return {
            "total_entries": total_entries,
            "entries_by_type": cache_by_type,
            "cache_ttl_settings": {qt.value: ttl for qt, ttl in self.cache_ttl.items()}
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all data sources"""
        health_results = {}
        
        for source_name, source in self.sources.items():
            try:
                # Try a simple operation to test health
                start_time = time.time()
                
                # For now, just check if the source is responsive
                # In a real implementation, you might make a lightweight API call
                health_results[source_name] = {
                    "status": "healthy",
                    "response_time_ms": (time.time() - start_time) * 1000,
                    "last_check": datetime.utcnow().isoformat()
                }
                
            except Exception as e:
                health_results[source_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "last_check": datetime.utcnow().isoformat()
                }
        
        return health_results
