#!/usr/bin/env python3
"""
Financial Analyst Chatbot Server
Simple script to run the FastAPI server with proper configuration
"""
import os
import sys
import uvicorn
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Main entry point"""
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("📝 Please copy .env.example to .env and configure your API keys:")
        print("   cp .env.example .env")
        print("\n🔑 Required API keys:")
        print("   - OPENAI_API_KEY (from https://platform.openai.com/api-keys)")
        print("   - ALPHA_VANTAGE_API_KEY (from https://www.alphavantage.co/support/#api-key)")
        print("   - FINNHUB_API_KEY (from https://finnhub.io/register)")
        print("   - QDRANT_URL and QDRANT_API_KEY (from https://cloud.qdrant.io/)")
        return 1
    
    # Import settings to validate configuration
    try:
        from src.config.settings import get_settings
        settings = get_settings()
        print("✅ Configuration loaded successfully")
        print(f"🌍 Environment: {settings.environment}")
        print(f"📊 Log level: {settings.log_level}")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        print("🔧 Please check your .env file configuration")
        return 1
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    print("\n🚀 Starting Financial Analyst Chatbot...")
    print(f"🌐 Server will be available at: http://localhost:8000")
    print(f"📚 API documentation at: http://localhost:8000/docs")
    print(f"💬 Chat interface at: http://localhost:8000")
    print("\n⏹️  Press Ctrl+C to stop the server\n")
    
    # Run the server
    try:
        uvicorn.run(
            "src.api.main:app",
            host="0.0.0.0",
            port=8000,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return 0
    except Exception as e:
        print(f"❌ Server error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
