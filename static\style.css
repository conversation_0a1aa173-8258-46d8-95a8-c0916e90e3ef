/* Financial Analyst Cha<PERSON>bot Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    flex: 1;
}

.chat-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 600px;
    min-height: 400px;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
}

.message-content {
    max-width: 80%;
    padding: 15px 20px;
    border-radius: 20px;
    line-height: 1.5;
}

.user-message {
    justify-content: flex-end;
}

.user-message .message-content {
    background: #667eea;
    color: white;
    border-bottom-right-radius: 5px;
}

.bot-message .message-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
}

.message-meta {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.data-sources {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.source-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.chat-input-container {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.input-group {
    display: flex;
    gap: 10px;
}

#chatInput {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s;
}

#chatInput:focus {
    border-color: #667eea;
}

#sendButton {
    padding: 15px 25px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s;
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#sendButton:hover:not(:disabled) {
    background: #5a6fd8;
}

#sendButton:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.status-panel, .examples-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.status-panel h3, .examples-panel h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #666;
}

.status-value {
    font-size: 0.9rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status-healthy {
    background: #d4edda;
    color: #155724;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}

.status-unknown {
    background: #fff3cd;
    color: #856404;
}

.example-queries {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.example-btn {
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    text-align: left;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.example-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
    color: #667eea;
}

.loading {
    opacity: 0.7;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .sidebar {
        order: -1;
        flex-direction: row;
        overflow-x: auto;
    }
    
    .status-panel, .examples-panel {
        min-width: 250px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .message-content {
        max-width: 90%;
    }
}
