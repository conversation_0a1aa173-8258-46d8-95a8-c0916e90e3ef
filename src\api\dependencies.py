"""
FastAPI Dependencies
Provides dependency injection for services and configurations
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from functools import lru_cache

from ..config.settings import Settings, get_settings
from ..config.logging_config import get_logger


logger = get_logger("dependencies")


@lru_cache()
def get_cached_settings() -> Settings:
    """Get cached settings instance"""
    return get_settings()


async def verify_api_key(settings: Settings = Depends(get_cached_settings)) -> Settings:
    """Verify that required API keys are configured"""
    missing_keys = []
    
    if not settings.openai_api_key:
        missing_keys.append("OPENAI_API_KEY")
    
    if not settings.alpha_vantage_api_key:
        missing_keys.append("ALPHA_VANTAGE_API_KEY")
    
    if not settings.finnhub_api_key:
        missing_keys.append("FINNHUB_API_KEY")
    
    if not settings.qdrant_url or not settings.qdrant_api_key:
        missing_keys.append("QDRANT_URL or QDRANT_API_KEY")
    
    if missing_keys:
        logger.error(f"Missing required API keys: {', '.join(missing_keys)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Missing required configuration: {', '.join(missing_keys)}"
        )
    
    return settings


async def get_rate_limiter():
    """Get rate limiter instance"""
    # This will be implemented when we create the rate limiter
    pass


async def get_cache_client():
    """Get cache client instance"""
    # This will be implemented when we create the cache client
    pass


async def get_vector_db_client():
    """Get vector database client instance"""
    # This will be implemented when we create the Qdrant client
    pass


async def get_data_source_manager():
    """Get data source manager instance"""
    # This will be implemented when we create the data source manager
    pass


async def get_query_processor():
    """Get query processor instance"""
    # This will be implemented when we create the query processor
    pass
