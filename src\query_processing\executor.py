"""
Query Executor
Orchestrates parallel API calls and coordinates data retrieval from multiple sources
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .parser import ParsedQuery, QueryType
from .router import RoutingStrategy
from ..utils.data_source_manager import DataSourceManager
from ..data_sources.base import DataSourceResponse
from ..config.logging_config import get_logger


@dataclass
class ExecutionResult:
    """Result of query execution"""
    success: bool
    data: Dict[str, Any]
    sources_used: List[str]
    execution_time_ms: float
    errors: List[str]
    warnings: List[str]
    metadata: Dict[str, Any]


@dataclass
class ExecutionPlan:
    """Plan for executing a query"""
    query_type: QueryType
    primary_operations: List[Tuple[str, callable]]  # (description, operation)
    parallel_operations: List[Tuple[str, callable]]  # Operations that can run in parallel
    fallback_operations: List[Tuple[str, callable]]  # Fallback operations
    estimated_time_ms: float
    required_data: List[str]


class QueryExecutor:
    """Orchestrates query execution with parallel API calls and error handling"""
    
    def __init__(self, data_source_manager: DataSourceManager):
        self.data_manager = data_source_manager
        self.logger = get_logger("query_executor")
        
        # Execution statistics
        self.execution_stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "avg_execution_time_ms": 0.0,
            "queries_by_type": {}
        }
    
    async def execute_query(self, parsed_query: ParsedQuery) -> ExecutionResult:
        """Execute a parsed query with optimal orchestration"""
        start_time = time.time()
        
        self.logger.info(
            f"Executing query: type={parsed_query.query_type.value}, "
            f"symbols={parsed_query.symbols}, confidence={parsed_query.confidence:.2f}"
        )
        
        try:
            # Create execution plan
            execution_plan = self._create_execution_plan(parsed_query)
            
            # Execute the plan
            result = await self._execute_plan(parsed_query, execution_plan)
            
            # Update statistics
            execution_time = (time.time() - start_time) * 1000
            self._update_stats(parsed_query.query_type, True, execution_time)
            
            result.execution_time_ms = execution_time
            
            self.logger.info(
                f"Query executed successfully in {execution_time:.0f}ms, "
                f"sources used: {result.sources_used}"
            )
            
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self._update_stats(parsed_query.query_type, False, execution_time)
            
            self.logger.error(f"Query execution failed: {str(e)}")
            
            return ExecutionResult(
                success=False,
                data={},
                sources_used=[],
                execution_time_ms=execution_time,
                errors=[f"Execution failed: {str(e)}"],
                warnings=[],
                metadata={"query_type": parsed_query.query_type.value}
            )
    
    def _create_execution_plan(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Create an execution plan based on the parsed query"""
        
        if parsed_query.query_type == QueryType.STOCK_PRICE:
            return self._plan_stock_price_query(parsed_query)
        elif parsed_query.query_type == QueryType.COMPANY_FUNDAMENTALS:
            return self._plan_fundamentals_query(parsed_query)
        elif parsed_query.query_type == QueryType.FINANCIAL_STATEMENTS:
            return self._plan_statements_query(parsed_query)
        elif parsed_query.query_type == QueryType.COMPARISON:
            return self._plan_comparison_query(parsed_query)
        elif parsed_query.query_type == QueryType.TREND_ANALYSIS:
            return self._plan_trend_analysis_query(parsed_query)
        else:
            return self._plan_general_query(parsed_query)
    
    def _plan_stock_price_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for stock price queries"""
        operations = []
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get stock price for {symbol}",
                lambda s=symbol: self.data_manager.get_stock_price(s, RoutingStrategy.FASTEST)
            ))
        
        return ExecutionPlan(
            query_type=QueryType.STOCK_PRICE,
            primary_operations=operations,
            parallel_operations=operations if len(operations) > 1 else [],
            fallback_operations=[],
            estimated_time_ms=2000,
            required_data=["price", "change", "volume"]
        )
    
    def _plan_fundamentals_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for company fundamentals queries"""
        operations = []
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get fundamentals for {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type=QueryType.COMPANY_FUNDAMENTALS,
            primary_operations=operations,
            parallel_operations=operations if len(operations) > 1 else [],
            fallback_operations=[],
            estimated_time_ms=3000,
            required_data=["market_cap", "pe_ratio", "revenue", "profit_margin"]
        )
    
    def _plan_statements_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for financial statements queries"""
        operations = []
        statement_type = parsed_query.statement_type.value if parsed_query.statement_type else "income"
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get {statement_type} statement for {symbol}",
                lambda s=symbol, st=statement_type: self.data_manager.get_financial_statements(
                    s, st, "annual", RoutingStrategy.BEST_COVERAGE
                )
            ))
        
        return ExecutionPlan(
            query_type=QueryType.FINANCIAL_STATEMENTS,
            primary_operations=operations,
            parallel_operations=operations if len(operations) > 1 else [],
            fallback_operations=[],
            estimated_time_ms=4000,
            required_data=["financial_data", "reporting_period"]
        )
    
    def _plan_comparison_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for comparison queries"""
        if len(parsed_query.symbols) < 2:
            # Not enough symbols for comparison
            return self._plan_general_query(parsed_query)
        
        # Get fundamentals for all symbols in parallel
        operations = []
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get data for {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type=QueryType.COMPARISON,
            primary_operations=[],
            parallel_operations=operations,
            fallback_operations=[],
            estimated_time_ms=5000,
            required_data=["comparative_metrics", "company_data"]
        )
    
    def _plan_trend_analysis_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for trend analysis queries"""
        operations = []
        
        # Determine date range
        if parsed_query.date_range:
            start_date, end_date = parsed_query.date_range
        else:
            # Default to last 2 years
            end_date = datetime.now()
            start_date = datetime(end_date.year - 2, end_date.month, end_date.day)
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get historical data for {symbol}",
                lambda s=symbol: self.data_manager.get_historical_data(
                    s, start_date, end_date, RoutingStrategy.BEST_COVERAGE
                )
            ))
        
        return ExecutionPlan(
            query_type=QueryType.TREND_ANALYSIS,
            primary_operations=operations,
            parallel_operations=operations if len(operations) > 1 else [],
            fallback_operations=[],
            estimated_time_ms=6000,
            required_data=["historical_prices", "trend_data"]
        )
    
    def _plan_general_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for general queries"""
        operations = []
        
        # Try to get basic information for any symbols found
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get basic info for {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type=QueryType.GENERAL,
            primary_operations=operations,
            parallel_operations=[],
            fallback_operations=[],
            estimated_time_ms=3000,
            required_data=["basic_info"]
        )
    
    async def _execute_plan(self, parsed_query: ParsedQuery, plan: ExecutionPlan) -> ExecutionResult:
        """Execute the execution plan"""
        results = {}
        sources_used = []
        errors = []
        warnings = []
        
        # Execute primary operations sequentially
        for description, operation in plan.primary_operations:
            try:
                self.logger.debug(f"Executing: {description}")
                response = await operation()
                
                if response.success:
                    results[description] = response.data
                    sources_used.append(response.source)
                else:
                    errors.append(f"{description}: {response.error}")
                    
            except Exception as e:
                errors.append(f"{description}: {str(e)}")
        
        # Execute parallel operations
        if plan.parallel_operations:
            parallel_tasks = []
            for description, operation in plan.parallel_operations:
                task = asyncio.create_task(self._execute_operation(description, operation))
                parallel_tasks.append(task)
            
            parallel_results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
            
            for i, result in enumerate(parallel_results):
                description = plan.parallel_operations[i][0]
                
                if isinstance(result, Exception):
                    errors.append(f"{description}: {str(result)}")
                elif isinstance(result, tuple):
                    success, data, source, error = result
                    if success:
                        results[description] = data
                        sources_used.append(source)
                    else:
                        errors.append(f"{description}: {error}")
        
        # Execute fallback operations if needed
        if not results and plan.fallback_operations:
            for description, operation in plan.fallback_operations:
                try:
                    self.logger.debug(f"Executing fallback: {description}")
                    response = await operation()
                    
                    if response.success:
                        results[description] = response.data
                        sources_used.append(response.source)
                        break  # Stop after first successful fallback
                    else:
                        warnings.append(f"Fallback {description}: {response.error}")
                        
                except Exception as e:
                    warnings.append(f"Fallback {description}: {str(e)}")
        
        # Determine overall success
        success = len(results) > 0
        
        # Prepare metadata
        metadata = {
            "query_type": plan.query_type.value,
            "symbols_requested": parsed_query.symbols,
            "operations_planned": len(plan.primary_operations) + len(plan.parallel_operations),
            "operations_successful": len(results),
            "estimated_time_ms": plan.estimated_time_ms,
            "required_data": plan.required_data
        }
        
        return ExecutionResult(
            success=success,
            data=results,
            sources_used=list(set(sources_used)),  # Remove duplicates
            execution_time_ms=0,  # Will be set by caller
            errors=errors,
            warnings=warnings,
            metadata=metadata
        )
    
    async def _execute_operation(self, description: str, operation) -> Tuple[bool, Any, str, str]:
        """Execute a single operation and return standardized result"""
        try:
            response = await operation()
            return (response.success, response.data, response.source, response.error or "")
        except Exception as e:
            return (False, None, "unknown", str(e))
    
    def _update_stats(self, query_type: QueryType, success: bool, execution_time_ms: float):
        """Update execution statistics"""
        self.execution_stats["total_queries"] += 1
        
        if success:
            self.execution_stats["successful_queries"] += 1
        else:
            self.execution_stats["failed_queries"] += 1
        
        # Update average execution time
        total = self.execution_stats["total_queries"]
        current_avg = self.execution_stats["avg_execution_time_ms"]
        self.execution_stats["avg_execution_time_ms"] = (
            (current_avg * (total - 1) + execution_time_ms) / total
        )
        
        # Update by query type
        type_key = query_type.value
        if type_key not in self.execution_stats["queries_by_type"]:
            self.execution_stats["queries_by_type"][type_key] = {
                "count": 0,
                "successful": 0,
                "avg_time_ms": 0.0
            }
        
        type_stats = self.execution_stats["queries_by_type"][type_key]
        type_stats["count"] += 1
        if success:
            type_stats["successful"] += 1
        
        # Update average time for this type
        type_count = type_stats["count"]
        current_type_avg = type_stats["avg_time_ms"]
        type_stats["avg_time_ms"] = (
            (current_type_avg * (type_count - 1) + execution_time_ms) / type_count
        )
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        stats = self.execution_stats.copy()
        
        # Calculate success rate
        total = stats["total_queries"]
        if total > 0:
            stats["success_rate"] = stats["successful_queries"] / total
        else:
            stats["success_rate"] = 0.0
        
        return stats
