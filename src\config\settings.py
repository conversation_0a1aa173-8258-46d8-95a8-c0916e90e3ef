"""
Pydantic Settings Configuration
Manages all environment variables and application settings with validation
"""
from typing import Optional
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4o-mini", env="OPENAI_MODEL")
    embedding_model: str = Field(default="text-embedding-3-small", env="EMBEDDING_MODEL")
    openai_max_tokens: int = Field(default=1000, env="OPENAI_MAX_TOKENS")
    openai_requests_per_minute: int = Field(default=50, env="OPENAI_REQUESTS_PER_MINUTE")
    
    # Financial API Keys
    alpha_vantage_api_key: str = Field(..., env="ALPHA_VANTAGE_API_KEY")
    finnhub_api_key: str = Field(..., env="FINNHUB_API_KEY")
    
    # Rate Limiting Settings
    alpha_vantage_requests_per_day: int = Field(default=25, env="ALPHA_VANTAGE_REQUESTS_PER_DAY")
    finnhub_requests_per_minute: int = Field(default=60, env="FINNHUB_REQUESTS_PER_MINUTE")
    
    # Qdrant Cloud Configuration
    qdrant_url: str = Field(..., env="QDRANT_URL")
    qdrant_api_key: str = Field(..., env="QDRANT_API_KEY")
    qdrant_collection_name: str = Field(default="financial_documents", env="QDRANT_COLLECTION_NAME")
    
    # Redis Configuration
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # Application Settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    max_concurrent_requests: int = Field(default=5, env="MAX_CONCURRENT_REQUESTS")
    cache_ttl_seconds: int = Field(default=3600, env="CACHE_TTL_SECONDS")
    api_rate_limit_per_minute: int = Field(default=50, env="API_RATE_LIMIT_PER_MINUTE")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # Vector Database Settings
    embedding_dimension: int = Field(default=1536, env="EMBEDDING_DIMENSION")
    similarity_threshold: float = Field(default=0.75, env="SIMILARITY_THRESHOLD")
    chunk_size: int = Field(default=800, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=150, env="CHUNK_OVERLAP")
    
    # FastAPI Settings
    app_name: str = Field(default="Financial Analyst Chatbot", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()

    @field_validator("similarity_threshold")
    @classmethod
    def validate_similarity_threshold(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
        return v

    @field_validator("chunk_overlap")
    @classmethod
    def validate_chunk_overlap(cls, v, info):
        if info.data and "chunk_size" in info.data and v >= info.data["chunk_size"]:
            raise ValueError("Chunk overlap must be less than chunk size")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings instance"""
    return settings
