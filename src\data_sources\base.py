"""
Abstract Base Class for Data Sources
Defines the interface for all financial data sources with error handling and rate limiting
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
import time
from dataclasses import dataclass
from enum import Enum

from ..config.logging_config import get_logger


class DataSourceType(Enum):
    """Types of data sources"""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    FALLBACK = "fallback"


class QueryType(Enum):
    """Types of financial queries"""
    STOCK_PRICE = "stock_price"
    COMPANY_FUNDAMENTALS = "company_fundamentals"
    FINANCIAL_STATEMENTS = "financial_statements"
    MARKET_DATA = "market_data"
    NEWS = "news"
    EARNINGS = "earnings"
    HISTORICAL_DATA = "historical_data"


@dataclass
class DataSourceResponse:
    """Standardized response from data sources"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    source: Optional[str] = None
    timestamp: Optional[datetime] = None
    cached: bool = False
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class RateLimitInfo:
    """Rate limiting information for data sources"""
    requests_per_minute: int
    requests_per_day: int
    current_minute_count: int = 0
    current_day_count: int = 0
    last_request_time: Optional[datetime] = None
    last_reset_time: Optional[datetime] = None


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreaker:
    """Circuit breaker for API failure handling"""
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    state: CircuitBreakerState = CircuitBreakerState.CLOSED
    
    def record_success(self):
        """Record successful API call"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def record_failure(self):
        """Record failed API call"""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
    
    def can_execute(self) -> bool:
        """Check if API call can be executed"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        if self.state == CircuitBreakerState.OPEN:
            if (datetime.utcnow() - self.last_failure_time).seconds >= self.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        
        # HALF_OPEN state - allow one request to test
        return True


class BaseDataSource(ABC):
    """Abstract base class for all financial data sources"""
    
    def __init__(
        self,
        name: str,
        source_type: DataSourceType,
        api_key: Optional[str] = None,
        rate_limit_info: Optional[RateLimitInfo] = None
    ):
        self.name = name
        self.source_type = source_type
        self.api_key = api_key
        self.rate_limit_info = rate_limit_info or RateLimitInfo(60, 1000)
        self.circuit_breaker = CircuitBreaker()
        self.logger = get_logger(f"data_source.{name.lower()}")
        self._cache: Dict[str, Any] = {}
        
    @abstractmethod
    async def get_stock_price(self, symbol: str) -> DataSourceResponse:
        """Get current stock price for a symbol"""
        pass
    
    @abstractmethod
    async def get_company_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get company fundamental data"""
        pass
    
    @abstractmethod
    async def get_financial_statements(
        self, 
        symbol: str, 
        statement_type: str,
        period: str = "annual"
    ) -> DataSourceResponse:
        """Get financial statements (income, balance, cash flow)"""
        pass
    
    @abstractmethod
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> DataSourceResponse:
        """Get historical price data"""
        pass
    
    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        now = datetime.utcnow()
        
        # Reset counters if needed
        if (self.rate_limit_info.last_reset_time is None or 
            (now - self.rate_limit_info.last_reset_time).seconds >= 60):
            self.rate_limit_info.current_minute_count = 0
            self.rate_limit_info.last_reset_time = now
        
        # Check daily limit (simplified - would need proper daily reset logic)
        if self.rate_limit_info.current_day_count >= self.rate_limit_info.requests_per_day:
            return False
        
        # Check minute limit
        if self.rate_limit_info.current_minute_count >= self.rate_limit_info.requests_per_minute:
            return False
        
        return True
    
    def _record_request(self):
        """Record a successful request for rate limiting"""
        self.rate_limit_info.current_minute_count += 1
        self.rate_limit_info.current_day_count += 1
        self.rate_limit_info.last_request_time = datetime.utcnow()
    
    async def _execute_with_circuit_breaker(self, func, *args, **kwargs) -> DataSourceResponse:
        """Execute function with circuit breaker pattern"""
        if not self.circuit_breaker.can_execute():
            return DataSourceResponse(
                success=False,
                error=f"Circuit breaker is OPEN for {self.name}",
                source=self.name
            )
        
        if not self._check_rate_limit():
            return DataSourceResponse(
                success=False,
                error=f"Rate limit exceeded for {self.name}",
                source=self.name
            )
        
        try:
            start_time = time.time()
            result = await func(*args, **kwargs)
            response_time = time.time() - start_time
            
            self._record_request()
            self.circuit_breaker.record_success()
            
            self.logger.info(
                f"API call successful",
                extra={
                    "source": self.name,
                    "function": func.__name__,
                    "response_time_ms": round(response_time * 1000, 2)
                }
            )
            
            return result
            
        except Exception as e:
            self.circuit_breaker.record_failure()
            error_msg = f"API call failed: {str(e)}"
            
            self.logger.error(
                error_msg,
                extra={
                    "source": self.name,
                    "function": func.__name__,
                    "error": str(e)
                }
            )
            
            return DataSourceResponse(
                success=False,
                error=error_msg,
                source=self.name
            )
    
    def get_cache_key(self, method: str, **kwargs) -> str:
        """Generate cache key for method and parameters"""
        key_parts = [self.name, method]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        return ":".join(key_parts)
    
    def get_cached_response(self, cache_key: str, ttl_seconds: int = 3600) -> Optional[DataSourceResponse]:
        """Get cached response if available and not expired"""
        if cache_key in self._cache:
            cached_data, timestamp = self._cache[cache_key]
            if (datetime.utcnow() - timestamp).seconds < ttl_seconds:
                cached_data.cached = True
                return cached_data
            else:
                # Remove expired cache entry
                del self._cache[cache_key]
        return None
    
    def cache_response(self, cache_key: str, response: DataSourceResponse):
        """Cache response data"""
        self._cache[cache_key] = (response, datetime.utcnow())
