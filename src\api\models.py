"""
Pydantic Models for API Requests and Responses
Defines all request/response schemas with validation
"""
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class QueryType(str, Enum):
    """Types of financial queries"""
    STOCK_PRICE = "stock_price"
    COMPANY_FUNDAMENTALS = "company_fundamentals"
    FINANCIAL_STATEMENTS = "financial_statements"
    COMPARISON = "comparison"
    TREND_ANALYSIS = "trend_analysis"
    CALCULATION = "calculation"
    GENERAL = "general"


class StatementType(str, Enum):
    """Types of financial statements"""
    INCOME = "income"
    BALANCE = "balance"
    CASH_FLOW = "cash_flow"


class Period(str, Enum):
    """Financial reporting periods"""
    ANNUAL = "annual"
    QUARTERLY = "quarterly"


class DataSource(str, Enum):
    """Available data sources"""
    ALPHA_VANTAGE = "alpha_vantage"
    FINNHUB = "finnhub"
    YFINANCE = "yfinance"
    VECTOR_DB = "vector_db"


# Request Models
class ChatRequest(BaseModel):
    """Chat request model"""
    query: str = Field(..., min_length=1, max_length=1000, description="User query")
    session_id: Optional[str] = Field(None, description="Session identifier for context")
    include_sources: bool = Field(default=True, description="Include data sources in response")
    
    @validator("query")
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()


class StockPriceRequest(BaseModel):
    """Stock price request model"""
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    
    @validator("symbol")
    def validate_symbol(cls, v):
        return v.upper().strip()


class CompanyFundamentalsRequest(BaseModel):
    """Company fundamentals request model"""
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    
    @validator("symbol")
    def validate_symbol(cls, v):
        return v.upper().strip()


class FinancialStatementsRequest(BaseModel):
    """Financial statements request model"""
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    statement_type: StatementType = Field(..., description="Type of financial statement")
    period: Period = Field(default=Period.ANNUAL, description="Reporting period")
    
    @validator("symbol")
    def validate_symbol(cls, v):
        return v.upper().strip()


class ComparisonRequest(BaseModel):
    """Company comparison request model"""
    symbols: List[str] = Field(..., min_items=2, max_items=5, description="Stock symbols to compare")
    metrics: List[str] = Field(..., min_items=1, description="Metrics to compare")
    period: Period = Field(default=Period.ANNUAL, description="Reporting period")
    
    @validator("symbols")
    def validate_symbols(cls, v):
        return [symbol.upper().strip() for symbol in v]


class HistoricalDataRequest(BaseModel):
    """Historical data request model"""
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    start_date: datetime = Field(..., description="Start date")
    end_date: datetime = Field(..., description="End date")
    
    @validator("symbol")
    def validate_symbol(cls, v):
        return v.upper().strip()
    
    @validator("end_date")
    def validate_date_range(cls, v, values):
        if "start_date" in values and v <= values["start_date"]:
            raise ValueError("End date must be after start date")
        return v


# Response Models
class DataSourceInfo(BaseModel):
    """Information about data source used"""
    name: str
    type: str
    cached: bool = False
    response_time_ms: Optional[float] = None
    error: Optional[str] = None


class ChatResponse(BaseModel):
    """Chat response model"""
    response: str = Field(..., description="Generated response")
    query_type: QueryType = Field(..., description="Detected query type")
    data_sources: List[DataSourceInfo] = Field(default=[], description="Data sources used")
    processing_time_ms: float = Field(..., description="Total processing time")
    session_id: Optional[str] = Field(None, description="Session identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class StockPriceResponse(BaseModel):
    """Stock price response model"""
    symbol: str
    price: float
    currency: str = "USD"
    change: Optional[float] = None
    change_percent: Optional[float] = None
    timestamp: datetime
    source: DataSource


class CompanyInfo(BaseModel):
    """Company information model"""
    symbol: str
    name: str
    sector: Optional[str] = None
    industry: Optional[str] = None
    market_cap: Optional[float] = None
    employees: Optional[int] = None
    description: Optional[str] = None


class FinancialMetric(BaseModel):
    """Financial metric model"""
    name: str
    value: Union[float, int, str]
    unit: Optional[str] = None
    period: Optional[str] = None
    year: Optional[int] = None


class FinancialStatement(BaseModel):
    """Financial statement model"""
    symbol: str
    statement_type: StatementType
    period: Period
    year: int
    metrics: List[FinancialMetric]
    source: DataSource


class ComparisonResult(BaseModel):
    """Comparison result model"""
    symbols: List[str]
    metrics: List[str]
    data: Dict[str, Dict[str, Any]]
    analysis: Optional[str] = None
    period: Period
    sources: List[DataSource]


class HistoricalDataPoint(BaseModel):
    """Historical data point model"""
    date: datetime
    open: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    close: float
    volume: Optional[int] = None


class HistoricalDataResponse(BaseModel):
    """Historical data response model"""
    symbol: str
    start_date: datetime
    end_date: datetime
    data: List[HistoricalDataPoint]
    source: DataSource


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = "1.0.0"
    services: Dict[str, str] = Field(default_factory=dict)


class DocumentUploadRequest(BaseModel):
    """Document upload request model"""
    filename: str = Field(..., description="Document filename")
    document_type: str = Field(..., description="Type of document (10-K, 10-Q, etc.)")
    company_symbol: Optional[str] = Field(None, description="Associated company symbol")
    filing_date: Optional[datetime] = Field(None, description="Filing date")


class DocumentUploadResponse(BaseModel):
    """Document upload response model"""
    document_id: str = Field(..., description="Unique document identifier")
    filename: str
    chunks_created: int = Field(..., description="Number of text chunks created")
    embeddings_generated: int = Field(..., description="Number of embeddings generated")
    processing_time_ms: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)
