# Financial Analyst Chatbot

A comprehensive financial analyst chatbot with multi-source data integration and intelligent query routing using modern Python-based architecture.

## Features

- **Multi-Source Data Integration**: Alpha Vantage, Finnhub, Yahoo Finance with intelligent failover
- **Vector Database**: Qdrant Cloud for document embeddings and similarity search
- **LLM Integration**: OpenAI GPT-4o-mini for responses, text-embedding-3-small for embeddings
- **Intelligent Query Routing**: Automatic data source selection based on query type
- **Rate Limiting**: Token bucket implementation with per-API specific limits
- **Circuit Breaker**: Automatic failover when APIs are down
- **Comprehensive Logging**: Structured JSON logging for debugging and monitoring
- **Modern Frontend**: Responsive HTML/JavaScript SPA

## Architecture

```
financial_chatbot/
├── src/
│   ├── config/          # Configuration and logging
│   ├── data_sources/    # Financial API clients
│   ├── vector_db/       # Qdrant Cloud integration
│   ├── query_processing/# Query parsing and routing
│   ├── api/            # FastAPI application
│   └── utils/          # Utilities and helpers
├── static/             # Frontend assets
├── tests/              # Test suite
└── requirements.txt    # Dependencies
```

## Quick Start

### 1. Environment Setup

```bash
# Clone and setup
git clone <repository>
cd financial_chatbot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Required API keys:
- **OpenAI API Key**: Get from https://platform.openai.com/api-keys
- **Alpha Vantage API Key**: Get from https://www.alphavantage.co/support/#api-key
- **Finnhub API Key**: Get from https://finnhub.io/register
- **Qdrant Cloud**: Create account at https://cloud.qdrant.io/

### 3. Qdrant Cloud Setup

1. Create account at https://cloud.qdrant.io/
2. Create a new cluster (free tier available)
3. Get your cluster URL and API key
4. Update `.env` with Qdrant credentials

### 4. Run the Application

```bash
# Development mode
python -m uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000

# Production mode
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000
```

Access the application at http://localhost:8000

## API Endpoints

### Chat Endpoint
```http
POST /api/chat
Content-Type: application/json

{
    "query": "What was Apple's revenue in Q3 2023?",
    "session_id": "optional_session_id",
    "include_sources": true
}
```

### Direct Data Endpoints
- `POST /api/stock-price` - Get current stock price
- `POST /api/company-fundamentals` - Get company fundamentals
- `POST /api/financial-statements` - Get financial statements
- `POST /api/compare` - Compare multiple companies
- `POST /api/historical-data` - Get historical data

### System Endpoints
- `GET /health` - Health check
- `GET /` - Frontend application

## Data Sources

### Primary Sources (with failover)
1. **Alpha Vantage** (25 requests/day)
   - Company fundamentals
   - Financial statements
   - Balance sheet, income statement, cash flow

2. **Finnhub** (60 calls/minute)
   - Real-time market data
   - Earnings transcripts
   - Financial news

3. **Yahoo Finance** (via yfinance)
   - Stock prices
   - Historical data
   - Company information (backup/fallback)

### Rate Limiting
- Token bucket algorithm per API
- Automatic request throttling
- Circuit breaker for failed endpoints
- Intelligent caching (1 hour market data, 24 hours fundamentals)

## Query Examples

### Stock Price Queries
- "What is Apple's current stock price?"
- "Show me TSLA price"

### Financial Analysis
- "What was Microsoft's revenue in Q3 2023?"
- "Show me Amazon's cash flow trends"
- "Compare Tesla vs Ford gross margins 2020-2023"

### Complex Analysis
- "Analyze Apple's financial performance over the last 5 years"
- "What are the key financial metrics for Google?"

## Development

### Running Tests
```bash
# Unit tests
pytest tests/test_data_sources.py -v

# Integration tests
pytest tests/test_query_processing.py -v

# API tests
pytest tests/test_api.py -v

# All tests
pytest -v
```

### Code Quality
```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

### Adding New Data Sources

1. Create new client in `src/data_sources/`
2. Inherit from `BaseDataSource`
3. Implement required methods
4. Add to data source manager
5. Update routing logic

## Monitoring and Logging

### Log Files
- `logs/financial_chatbot.log` - Application logs
- `logs/errors.log` - Error logs only

### Structured Logging
All logs are in JSON format with:
- Timestamp
- Log level
- Module/function
- API call metrics
- Error details

### Health Monitoring
- `/health` endpoint for system status
- Circuit breaker status
- API rate limit status
- Service availability

## Performance Targets

- **Response Time**: <3 seconds simple queries, <10 seconds complex
- **Accuracy**: 95%+ for financial data retrieval
- **Reliability**: 99.5% uptime with graceful degradation
- **Cost Efficiency**: <$15/month for 200 queries/day

## Deployment

### Environment Variables
See `.env.example` for all required configuration.

### Production Considerations
- Use Redis Cloud for caching
- Configure proper CORS origins
- Set up monitoring and alerting
- Use environment-specific logging levels
- Implement proper secret management

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Verify all API keys in `.env`
   - Check API key quotas and limits

2. **Qdrant Connection Issues**
   - Verify Qdrant Cloud URL and API key
   - Check network connectivity

3. **Rate Limiting**
   - Monitor API usage in logs
   - Adjust rate limits in configuration

4. **Performance Issues**
   - Enable Redis caching
   - Monitor query processing times
   - Check API response times

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request

## License

MIT License - see LICENSE file for details.
