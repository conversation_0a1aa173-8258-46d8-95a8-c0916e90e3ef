"""
Data Source Router with Intelligent Selection
Routes queries to optimal data sources based on query type, data availability, and API status
"""
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from .parser import ParsedQuery, QueryType
from ..data_sources.base import DataSourceType, BaseDataSource
from ..config.logging_config import get_logger


class RoutingStrategy(Enum):
    """Data source routing strategies"""
    FASTEST = "fastest"
    MOST_RELIABLE = "most_reliable"
    BEST_COVERAGE = "best_coverage"
    COST_EFFECTIVE = "cost_effective"


@dataclass
class DataSourceCapability:
    """Data source capability information"""
    source_name: str
    source_type: DataSourceType
    supports_real_time: bool
    supports_fundamentals: bool
    supports_statements: bool
    supports_historical: bool
    supports_news: bool
    rate_limit_per_minute: int
    rate_limit_per_day: int
    typical_response_time_ms: float
    reliability_score: float  # 0.0 to 1.0
    data_quality_score: float  # 0.0 to 1.0
    cost_per_request: float


@dataclass
class RoutingDecision:
    """Data source routing decision"""
    primary_source: str
    fallback_sources: List[str]
    strategy_used: RoutingStrategy
    confidence: float
    estimated_response_time_ms: float
    reasoning: List[str]


class DataSourceRouter:
    """Intelligent data source router"""
    
    def __init__(self):
        self.logger = get_logger("data_source_router")
        
        # Define data source capabilities
        self.capabilities = {
            "Alpha Vantage": DataSourceCapability(
                source_name="Alpha Vantage",
                source_type=DataSourceType.PRIMARY,
                supports_real_time=True,
                supports_fundamentals=True,
                supports_statements=True,
                supports_historical=True,
                supports_news=False,
                rate_limit_per_minute=5,
                rate_limit_per_day=25,
                typical_response_time_ms=2000,
                reliability_score=0.85,
                data_quality_score=0.9,
                cost_per_request=0.0
            ),
            "Finnhub": DataSourceCapability(
                source_name="Finnhub",
                source_type=DataSourceType.SECONDARY,
                supports_real_time=True,
                supports_fundamentals=True,
                supports_statements=False,
                supports_historical=True,
                supports_news=True,
                rate_limit_per_minute=60,
                rate_limit_per_day=1000,
                typical_response_time_ms=1500,
                reliability_score=0.8,
                data_quality_score=0.85,
                cost_per_request=0.0
            ),
            "Yahoo Finance": DataSourceCapability(
                source_name="Yahoo Finance",
                source_type=DataSourceType.FALLBACK,
                supports_real_time=True,
                supports_fundamentals=True,
                supports_statements=True,
                supports_historical=True,
                supports_news=False,
                rate_limit_per_minute=30,
                rate_limit_per_day=500,
                typical_response_time_ms=3000,
                reliability_score=0.7,
                data_quality_score=0.75,
                cost_per_request=0.0
            )
        }
        
        # Track data source health and performance
        self.source_health = {name: 1.0 for name in self.capabilities.keys()}
        self.source_response_times = {name: [] for name in self.capabilities.keys()}
    
    async def route_query(
        self, 
        parsed_query: ParsedQuery,
        strategy: RoutingStrategy = RoutingStrategy.BEST_COVERAGE,
        available_sources: Optional[List[str]] = None
    ) -> RoutingDecision:
        """Route query to optimal data sources"""
        
        self.logger.info(
            f"Routing query: type={parsed_query.query_type.value}, "
            f"symbols={parsed_query.symbols}, strategy={strategy.value}"
        )
        
        # Filter available sources
        if available_sources is None:
            available_sources = list(self.capabilities.keys())
        
        # Get compatible sources for this query type
        compatible_sources = self._get_compatible_sources(parsed_query, available_sources)
        
        if not compatible_sources:
            return RoutingDecision(
                primary_source="Yahoo Finance",  # Default fallback
                fallback_sources=[],
                strategy_used=strategy,
                confidence=0.1,
                estimated_response_time_ms=5000,
                reasoning=["No compatible sources found - using default fallback"]
            )
        
        # Apply routing strategy
        if strategy == RoutingStrategy.FASTEST:
            decision = self._route_for_speed(parsed_query, compatible_sources)
        elif strategy == RoutingStrategy.MOST_RELIABLE:
            decision = self._route_for_reliability(parsed_query, compatible_sources)
        elif strategy == RoutingStrategy.BEST_COVERAGE:
            decision = self._route_for_coverage(parsed_query, compatible_sources)
        elif strategy == RoutingStrategy.COST_EFFECTIVE:
            decision = self._route_for_cost(parsed_query, compatible_sources)
        else:
            decision = self._route_for_coverage(parsed_query, compatible_sources)
        
        decision.strategy_used = strategy
        
        self.logger.info(
            f"Routing decision: primary={decision.primary_source}, "
            f"fallbacks={decision.fallback_sources}, confidence={decision.confidence:.2f}"
        )
        
        return decision
    
    def _get_compatible_sources(self, parsed_query: ParsedQuery, available_sources: List[str]) -> List[str]:
        """Get data sources compatible with the query type"""
        compatible = []
        
        for source_name in available_sources:
            if source_name not in self.capabilities:
                continue
                
            capability = self.capabilities[source_name]
            
            # Check if source supports the query type
            is_compatible = False
            
            if parsed_query.query_type == QueryType.STOCK_PRICE:
                is_compatible = capability.supports_real_time
            elif parsed_query.query_type == QueryType.COMPANY_FUNDAMENTALS:
                is_compatible = capability.supports_fundamentals
            elif parsed_query.query_type == QueryType.FINANCIAL_STATEMENTS:
                is_compatible = capability.supports_statements
            elif parsed_query.query_type == QueryType.TREND_ANALYSIS:
                is_compatible = capability.supports_historical
            elif parsed_query.query_type == QueryType.NEWS:
                is_compatible = capability.supports_news
            elif parsed_query.query_type == QueryType.COMPARISON:
                # For comparison, need fundamentals or real-time data
                is_compatible = capability.supports_fundamentals or capability.supports_real_time
            else:
                # General queries can use any source
                is_compatible = True
            
            # Check source health
            if is_compatible and self.source_health[source_name] > 0.3:
                compatible.append(source_name)
        
        return compatible
    
    def _route_for_speed(self, parsed_query: ParsedQuery, compatible_sources: List[str]) -> RoutingDecision:
        """Route for fastest response time"""
        # Sort by response time and health
        sources_by_speed = sorted(
            compatible_sources,
            key=lambda s: (
                self.capabilities[s].typical_response_time_ms * (2 - self.source_health[s])
            )
        )
        
        primary = sources_by_speed[0]
        fallbacks = sources_by_speed[1:3]  # Top 2 fallbacks
        
        estimated_time = self.capabilities[primary].typical_response_time_ms
        confidence = self.source_health[primary] * 0.8
        
        reasoning = [
            f"Selected {primary} for fastest response time ({estimated_time:.0f}ms)",
            f"Source health: {self.source_health[primary]:.2f}"
        ]
        
        return RoutingDecision(
            primary_source=primary,
            fallback_sources=fallbacks,
            strategy_used=RoutingStrategy.FASTEST,
            confidence=confidence,
            estimated_response_time_ms=estimated_time,
            reasoning=reasoning
        )
    
    def _route_for_reliability(self, parsed_query: ParsedQuery, compatible_sources: List[str]) -> RoutingDecision:
        """Route for most reliable source"""
        # Sort by reliability score and health
        sources_by_reliability = sorted(
            compatible_sources,
            key=lambda s: (
                self.capabilities[s].reliability_score * self.source_health[s]
            ),
            reverse=True
        )
        
        primary = sources_by_reliability[0]
        fallbacks = sources_by_reliability[1:3]
        
        capability = self.capabilities[primary]
        estimated_time = capability.typical_response_time_ms
        confidence = capability.reliability_score * self.source_health[primary]
        
        reasoning = [
            f"Selected {primary} for highest reliability ({capability.reliability_score:.2f})",
            f"Current health: {self.source_health[primary]:.2f}"
        ]
        
        return RoutingDecision(
            primary_source=primary,
            fallback_sources=fallbacks,
            strategy_used=RoutingStrategy.MOST_RELIABLE,
            confidence=confidence,
            estimated_response_time_ms=estimated_time,
            reasoning=reasoning
        )
    
    def _route_for_coverage(self, parsed_query: ParsedQuery, compatible_sources: List[str]) -> RoutingDecision:
        """Route for best data coverage and quality"""
        # Score sources based on data quality, reliability, and query-specific factors
        source_scores = {}
        
        for source_name in compatible_sources:
            capability = self.capabilities[source_name]
            health = self.source_health[source_name]
            
            # Base score from data quality and reliability
            score = (capability.data_quality_score * 0.4 + 
                    capability.reliability_score * 0.3 + 
                    health * 0.3)
            
            # Query-specific bonuses
            if parsed_query.query_type == QueryType.FINANCIAL_STATEMENTS:
                if source_name == "Alpha Vantage":
                    score += 0.2  # Alpha Vantage has best financial statements
            elif parsed_query.query_type == QueryType.STOCK_PRICE:
                if source_name == "Finnhub":
                    score += 0.1  # Finnhub has good real-time data
            elif parsed_query.query_type == QueryType.NEWS:
                if source_name == "Finnhub":
                    score += 0.3  # Only Finnhub supports news
            
            # Rate limit considerations
            if capability.rate_limit_per_day < 50:
                score -= 0.1  # Penalize very low rate limits
            
            source_scores[source_name] = score
        
        # Sort by score
        sorted_sources = sorted(source_scores.items(), key=lambda x: x[1], reverse=True)
        
        primary = sorted_sources[0][0]
        fallbacks = [s[0] for s in sorted_sources[1:3]]
        
        capability = self.capabilities[primary]
        estimated_time = capability.typical_response_time_ms
        confidence = source_scores[primary]
        
        reasoning = [
            f"Selected {primary} for best coverage (score: {source_scores[primary]:.2f})",
            f"Data quality: {capability.data_quality_score:.2f}",
            f"Reliability: {capability.reliability_score:.2f}"
        ]
        
        return RoutingDecision(
            primary_source=primary,
            fallback_sources=fallbacks,
            strategy_used=RoutingStrategy.BEST_COVERAGE,
            confidence=confidence,
            estimated_response_time_ms=estimated_time,
            reasoning=reasoning
        )

    def _route_for_cost(self, parsed_query: ParsedQuery, compatible_sources: List[str]) -> RoutingDecision:
        """Route for most cost-effective option"""
        # Sort by cost per request (all are free, so use rate limits as proxy for "cost")
        sources_by_cost = sorted(
            compatible_sources,
            key=lambda s: (
                1.0 / self.capabilities[s].rate_limit_per_day,  # Lower daily limit = higher "cost"
                -self.capabilities[s].data_quality_score  # Prefer higher quality
            )
        )

        primary = sources_by_cost[0]
        fallbacks = sources_by_cost[1:3]

        capability = self.capabilities[primary]
        estimated_time = capability.typical_response_time_ms
        confidence = self.source_health[primary] * 0.7

        reasoning = [
            f"Selected {primary} for cost effectiveness",
            f"Daily rate limit: {capability.rate_limit_per_day}",
            f"Data quality: {capability.data_quality_score:.2f}"
        ]

        return RoutingDecision(
            primary_source=primary,
            fallback_sources=fallbacks,
            strategy_used=RoutingStrategy.COST_EFFECTIVE,
            confidence=confidence,
            estimated_response_time_ms=estimated_time,
            reasoning=reasoning
        )

    def update_source_health(self, source_name: str, success: bool, response_time_ms: float):
        """Update source health based on API call results"""
        if source_name not in self.source_health:
            return

        # Update health score
        current_health = self.source_health[source_name]

        if success:
            # Improve health on success
            self.source_health[source_name] = min(1.0, current_health + 0.1)
        else:
            # Degrade health on failure
            self.source_health[source_name] = max(0.0, current_health - 0.2)

        # Track response times
        response_times = self.source_response_times[source_name]
        response_times.append(response_time_ms)

        # Keep only last 10 response times
        if len(response_times) > 10:
            response_times.pop(0)

        # Update typical response time
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            self.capabilities[source_name].typical_response_time_ms = avg_response_time

        self.logger.debug(
            f"Updated {source_name} health: {self.source_health[source_name]:.2f}, "
            f"avg response time: {self.capabilities[source_name].typical_response_time_ms:.0f}ms"
        )

    def get_source_status(self) -> Dict[str, Dict[str, Any]]:
        """Get current status of all data sources"""
        status = {}

        for source_name, capability in self.capabilities.items():
            health = self.source_health[source_name]
            response_times = self.source_response_times[source_name]

            status[source_name] = {
                "health": health,
                "status": "healthy" if health > 0.7 else "degraded" if health > 0.3 else "unhealthy",
                "reliability_score": capability.reliability_score,
                "data_quality_score": capability.data_quality_score,
                "avg_response_time_ms": capability.typical_response_time_ms,
                "rate_limit_per_day": capability.rate_limit_per_day,
                "rate_limit_per_minute": capability.rate_limit_per_minute,
                "recent_response_times": response_times[-5:],  # Last 5 response times
                "capabilities": {
                    "real_time": capability.supports_real_time,
                    "fundamentals": capability.supports_fundamentals,
                    "statements": capability.supports_statements,
                    "historical": capability.supports_historical,
                    "news": capability.supports_news
                }
            }

        return status

    def recommend_sources_for_query_type(self, query_type: QueryType) -> List[Tuple[str, float]]:
        """Recommend best sources for a specific query type"""
        recommendations = []

        for source_name, capability in self.capabilities.items():
            score = 0.0

            # Check compatibility
            if query_type == QueryType.STOCK_PRICE and capability.supports_real_time:
                score = capability.reliability_score * 0.6 + (1.0 / capability.typical_response_time_ms) * 1000 * 0.4
            elif query_type == QueryType.COMPANY_FUNDAMENTALS and capability.supports_fundamentals:
                score = capability.data_quality_score * 0.7 + capability.reliability_score * 0.3
            elif query_type == QueryType.FINANCIAL_STATEMENTS and capability.supports_statements:
                score = capability.data_quality_score * 0.8 + capability.reliability_score * 0.2
            elif query_type == QueryType.TREND_ANALYSIS and capability.supports_historical:
                score = capability.data_quality_score * 0.5 + capability.reliability_score * 0.5
            elif query_type == QueryType.NEWS and capability.supports_news:
                score = capability.reliability_score * 0.6 + (1.0 / capability.typical_response_time_ms) * 1000 * 0.4

            if score > 0:
                # Apply health factor
                score *= self.source_health[source_name]
                recommendations.append((source_name, score))

        # Sort by score descending
        recommendations.sort(key=lambda x: x[1], reverse=True)

        return recommendations

    def get_routing_statistics(self) -> Dict[str, Any]:
        """Get routing statistics and performance metrics"""
        total_sources = len(self.capabilities)
        healthy_sources = sum(1 for health in self.source_health.values() if health > 0.7)

        avg_health = sum(self.source_health.values()) / len(self.source_health)

        source_rankings = []
        for source_name, capability in self.capabilities.items():
            health = self.source_health[source_name]
            overall_score = (
                capability.reliability_score * 0.3 +
                capability.data_quality_score * 0.3 +
                health * 0.4
            )
            source_rankings.append((source_name, overall_score))

        source_rankings.sort(key=lambda x: x[1], reverse=True)

        return {
            "total_sources": total_sources,
            "healthy_sources": healthy_sources,
            "average_health": avg_health,
            "best_source": source_rankings[0][0] if source_rankings else None,
            "source_rankings": source_rankings,
            "last_updated": datetime.utcnow().isoformat()
        }
