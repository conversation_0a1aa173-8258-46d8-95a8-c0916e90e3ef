<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Analyst Chatbot</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Financial Analyst Chatbot</h1>
            <p>Multi-source financial data integration with intelligent query routing</p>
        </header>

        <main>
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message bot-message">
                        <div class="message-content">
                            <p>Hello! I'm your financial analyst assistant. I can help you with:</p>
                            <ul>
                                <li>Stock prices and market data</li>
                                <li>Company fundamentals and financial statements</li>
                                <li>Financial analysis and comparisons</li>
                                <li>Historical data and trends</li>
                            </ul>
                            <p>Try asking me something like "What was Apple's revenue in Q3 2023?" or "Compare Tesla vs Ford gross margins"</p>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="chatInput" 
                            placeholder="Ask me about financial data..."
                            maxlength="1000"
                        >
                        <button id="sendButton" onclick="sendMessage()">
                            <span id="sendButtonText">Send</span>
                            <div id="sendButtonSpinner" class="spinner" style="display: none;"></div>
                        </button>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <div class="status-panel">
                    <h3>System Status</h3>
                    <div class="status-item">
                        <span class="status-label">API:</span>
                        <span class="status-value" id="apiStatus">Checking...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">OpenAI:</span>
                        <span class="status-value" id="openaiStatus">Unknown</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Qdrant:</span>
                        <span class="status-value" id="qdrantStatus">Unknown</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Alpha Vantage:</span>
                        <span class="status-value" id="alphaVantageStatus">Unknown</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Finnhub:</span>
                        <span class="status-value" id="finnhubStatus">Unknown</span>
                    </div>
                </div>

                <div class="examples-panel">
                    <h3>Example Queries</h3>
                    <div class="example-queries">
                        <button class="example-btn" onclick="setQuery('What is Apple\'s current stock price?')">
                            Apple stock price
                        </button>
                        <button class="example-btn" onclick="setQuery('Show me Microsoft\'s revenue for the last 3 years')">
                            Microsoft revenue trends
                        </button>
                        <button class="example-btn" onclick="setQuery('Compare Tesla and Ford gross margins')">
                            Tesla vs Ford comparison
                        </button>
                        <button class="example-btn" onclick="setQuery('What was Amazon\'s cash flow in Q4 2023?')">
                            Amazon cash flow
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
