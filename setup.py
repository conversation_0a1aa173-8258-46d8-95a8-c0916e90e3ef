#!/usr/bin/env python3
"""
Setup Script for Financial Analyst Chatbot
Helps users set up the environment and dependencies
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def check_virtual_environment():
    """Check if we're in a virtual environment"""
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    if not in_venv:
        print("⚠️  Not in a virtual environment")
        print("📝 Recommended: Create a virtual environment first:")
        print("   python -m venv venv")
        print("   source venv/bin/activate  # On Windows: venv\\Scripts\\activate")
        response = input("Continue anyway? (y/N): ")
        return response.lower() == 'y'
    print("✅ Virtual environment detected")
    return True


def install_dependencies():
    """Install Python dependencies"""
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )


def setup_environment_file():
    """Set up .env file from template"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example template not found")
        return False
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("🔑 Please edit .env file and add your API keys:")
        print("   - OPENAI_API_KEY")
        print("   - ALPHA_VANTAGE_API_KEY") 
        print("   - FINNHUB_API_KEY")
        print("   - QDRANT_URL and QDRANT_API_KEY")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def create_directories():
    """Create necessary directories"""
    directories = ["logs", "data", "uploads"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def run_basic_tests():
    """Run basic tests to verify setup"""
    if not Path("tests").exists():
        print("⚠️  Tests directory not found, skipping tests")
        return True
    
    return run_command(
        f"{sys.executable} -m pytest tests/test_basic_setup.py -v",
        "Running basic setup tests"
    )


def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup completed successfully!")
    print("\n📝 Next steps:")
    print("1. Edit .env file and add your API keys")
    print("2. Set up Qdrant Cloud account at https://cloud.qdrant.io/")
    print("3. Run the server: python run_server.py")
    print("4. Open http://localhost:8000 in your browser")
    print("\n🔗 Useful links:")
    print("   - OpenAI API Keys: https://platform.openai.com/api-keys")
    print("   - Alpha Vantage API: https://www.alphavantage.co/support/#api-key")
    print("   - Finnhub API: https://finnhub.io/register")
    print("   - Qdrant Cloud: https://cloud.qdrant.io/")


def main():
    """Main setup function"""
    print("🚀 Financial Analyst Chatbot Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check virtual environment
    if not check_virtual_environment():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return 1
    
    # Setup environment file
    if not setup_environment_file():
        print("❌ Failed to setup environment file")
        return 1
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        return 1
    
    # Run basic tests
    if not run_basic_tests():
        print("⚠️  Some tests failed, but setup can continue")
    
    # Print next steps
    print_next_steps()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
