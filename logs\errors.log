{"timestamp": "2025-06-05T09:28:40.901825Z", "level": "ERROR", "logger": "financial_chatbot.test", "message": "Test error message", "module": "test_phase1", "function": "test_logging_system", "line": 82, "taskName": "Task-1", "asctime": "2025-06-05 14:58:40,901"}
{"timestamp": "2025-06-05T10:07:10.527436Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:10,524"}
{"timestamp": "2025-06-05T10:07:11.239842Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from <PERSON> <PERSON>tage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:11,239"}
{"timestamp": "2025-06-05T10:07:15.172433Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for PRICE: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for PRICE: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:15,170"}
{"timestamp": "2025-06-05T10:07:15.487665Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:15,486"}
{"timestamp": "2025-06-05T10:07:15.747958Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:15,746"}
{"timestamp": "2025-06-05T10:07:17.876903Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for IS: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for IS: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:17,875"}
{"timestamp": "2025-06-05T10:07:18.194880Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:18,193"}
{"timestamp": "2025-06-05T10:07:18.447357Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:18,447"}
{"timestamp": "2025-06-05T10:07:20.592616Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for APPLE: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for APPLE: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:20,592"}
{"timestamp": "2025-06-05T10:07:20.904974Z", "level": "ERROR", "logger": "financial_chatbot.finnhub", "message": "API call failed: HTTP 401: {\"error\":\"Invalid API key.\"}", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Finnhub", "error": "HTTP 401: {\"error\":\"Invalid API key.\"}", "asctime": "2025-06-05 15:37:20,904"}
{"timestamp": "2025-06-05T10:07:21.169259Z", "level": "ERROR", "logger": "financial_chatbot.alpha_vantage", "message": "API call failed: Invalid response format from Alpha Vantage", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Alpha Vantage", "error": "Invalid response format from Alpha Vantage", "asctime": "2025-06-05 15:37:21,169"}
{"timestamp": "2025-06-05T10:07:23.702286Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for S: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for S: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:23,700"}
{"timestamp": "2025-06-05T10:07:26.506253Z", "level": "ERROR", "logger": "financial_chatbot.yfinance", "message": "API call failed: Failed to fetch historical data for AAPL: Too Many Requests. Rate limited. Try after a while.", "module": "base", "function": "_fetch_price", "line": 217, "taskName": "Task-8", "source": "Yahoo Finance", "error": "Failed to fetch historical data for AAPL: Too Many Requests. Rate limited. Try after a while.", "asctime": "2025-06-05 15:37:26,505"}
