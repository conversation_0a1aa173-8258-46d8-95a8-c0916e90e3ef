// Financial Analyst Chatbot Frontend JavaScript

class ChatBot {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.isProcessing = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkSystemHealth();
        this.focusInput();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    setupEventListeners() {
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');

        // Enter key to send message
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Send button click
        sendButton.addEventListener('click', () => this.sendMessage());

        // Auto-resize input (if needed for multi-line)
        chatInput.addEventListener('input', () => {
            this.adjustInputHeight();
        });
    }

    adjustInputHeight() {
        const input = document.getElementById('chatInput');
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 120) + 'px';
    }

    focusInput() {
        document.getElementById('chatInput').focus();
    }

    async sendMessage() {
        if (this.isProcessing) return;

        const input = document.getElementById('chatInput');
        const query = input.value.trim();

        if (!query) return;

        this.isProcessing = true;
        this.updateSendButton(true);

        // Add user message to chat
        this.addMessage(query, 'user');
        input.value = '';
        this.adjustInputHeight();

        try {
            const response = await this.callChatAPI(query);
            this.addMessage(response.response, 'bot', response);
        } catch (error) {
            console.error('Chat error:', error);
            this.addMessage(
                'Sorry, I encountered an error processing your request. Please try again.',
                'bot',
                null,
                true
            );
        } finally {
            this.isProcessing = false;
            this.updateSendButton(false);
            this.focusInput();
        }
    }

    async callChatAPI(query) {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query,
                session_id: this.sessionId,
                include_sources: true
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.detail || `HTTP ${response.status}`);
        }

        return await response.json();
    }

    addMessage(content, sender, metadata = null, isError = false) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        if (isError) {
            messageContent.className += ' error-message';
        }

        // Format content (basic markdown-like formatting)
        const formattedContent = this.formatContent(content);
        messageContent.innerHTML = formattedContent;

        messageDiv.appendChild(messageContent);

        // Add metadata for bot messages
        if (sender === 'bot' && metadata) {
            const metaDiv = document.createElement('div');
            metaDiv.className = 'message-meta';

            const timeSpan = document.createElement('span');
            timeSpan.textContent = `${metadata.processing_time_ms}ms`;

            const sourcesDiv = document.createElement('div');
            sourcesDiv.className = 'data-sources';

            if (metadata.data_sources && metadata.data_sources.length > 0) {
                metadata.data_sources.forEach(source => {
                    const sourceTag = document.createElement('span');
                    sourceTag.className = 'source-tag';
                    sourceTag.textContent = source.name;
                    if (source.cached) {
                        sourceTag.textContent += ' (cached)';
                    }
                    sourcesDiv.appendChild(sourceTag);
                });
            }

            metaDiv.appendChild(timeSpan);
            metaDiv.appendChild(sourcesDiv);
            messageDiv.appendChild(metaDiv);
        }

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatContent(content) {
        // Basic formatting for better readability
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    updateSendButton(isLoading) {
        const sendButton = document.getElementById('sendButton');
        const sendButtonText = document.getElementById('sendButtonText');
        const sendButtonSpinner = document.getElementById('sendButtonSpinner');

        sendButton.disabled = isLoading;

        if (isLoading) {
            sendButtonText.style.display = 'none';
            sendButtonSpinner.style.display = 'block';
        } else {
            sendButtonText.style.display = 'block';
            sendButtonSpinner.style.display = 'none';
        }
    }

    async checkSystemHealth() {
        try {
            const response = await fetch('/health');
            const healthData = await response.json();

            this.updateSystemStatus('apiStatus', 'healthy', 'Healthy');

            // Update individual service statuses
            if (healthData.services) {
                Object.entries(healthData.services).forEach(([service, status]) => {
                    const elementId = service + 'Status';
                    const element = document.getElementById(elementId);
                    if (element) {
                        this.updateSystemStatus(elementId, status, this.capitalizeFirst(status));
                    }
                });
            }
        } catch (error) {
            console.error('Health check failed:', error);
            this.updateSystemStatus('apiStatus', 'error', 'Error');
        }
    }

    updateSystemStatus(elementId, status, text) {
        const element = document.getElementById(elementId);
        if (!element) return;

        element.textContent = text;
        element.className = 'status-value';

        if (status === 'healthy') {
            element.classList.add('status-healthy');
        } else if (status === 'error' || status === 'unhealthy') {
            element.classList.add('status-error');
        } else {
            element.classList.add('status-unknown');
        }
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}

// Global functions for example buttons
function setQuery(query) {
    const input = document.getElementById('chatInput');
    input.value = query;
    input.focus();
}

function sendMessage() {
    if (window.chatBot) {
        window.chatBot.sendMessage();
    }
}

// Initialize chatbot when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chatBot = new ChatBot();
});
