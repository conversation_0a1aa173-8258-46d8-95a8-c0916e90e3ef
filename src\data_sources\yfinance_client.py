"""
Yahoo Finance Client (via yfinance library)
Provides backup/fallback access to stock data when primary APIs fail
No explicit rate limits but should be used conservatively
"""
import asyncio
import yfinance as yf
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import concurrent.futures

from .base import BaseDataSource, DataSourceResponse, DataSourceType, RateLimitInfo
from ..config.logging_config import get_logger


class YFinanceClient(BaseDataSource):
    """Yahoo Finance client using yfinance library as fallback data source"""
    
    def __init__(self):
        rate_limit_info = RateLimitInfo(
            requests_per_minute=30,  # Conservative rate limiting
            requests_per_day=500
        )
        
        super().__init__(
            name="Yahoo Finance",
            source_type=DataSourceType.FALLBACK,
            rate_limit_info=rate_limit_info
        )
        
        self.logger = get_logger("yfinance")
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=3)
    
    async def _run_in_executor(self, func, *args):
        """Run synchronous yfinance functions in thread executor"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, func, *args)
    
    def _get_ticker_info(self, symbol: str) -> Dict[str, Any]:
        """Get ticker info synchronously"""
        ticker = yf.Ticker(symbol)
        try:
            info = ticker.info
            if not info or "symbol" not in info:
                raise Exception(f"No data found for symbol {symbol}")
            return info
        except Exception as e:
            raise Exception(f"Failed to fetch data for {symbol}: {str(e)}")
    
    def _get_ticker_history(self, symbol: str, period: str = "1d") -> pd.DataFrame:
        """Get ticker history synchronously"""
        ticker = yf.Ticker(symbol)
        try:
            hist = ticker.history(period=period)
            if hist.empty:
                raise Exception(f"No historical data found for symbol {symbol}")
            return hist
        except Exception as e:
            raise Exception(f"Failed to fetch historical data for {symbol}: {str(e)}")
    
    def _get_ticker_financials(self, symbol: str, statement_type: str, quarterly: bool = False):
        """Get ticker financials synchronously"""
        ticker = yf.Ticker(symbol)
        try:
            if statement_type == "income":
                data = ticker.quarterly_financials if quarterly else ticker.financials
            elif statement_type == "balance":
                data = ticker.quarterly_balance_sheet if quarterly else ticker.balance_sheet
            elif statement_type == "cash_flow":
                data = ticker.quarterly_cashflow if quarterly else ticker.cashflow
            else:
                raise Exception(f"Invalid statement type: {statement_type}")
            
            if data.empty:
                raise Exception(f"No {statement_type} data found for symbol {symbol}")
            
            return data
        except Exception as e:
            raise Exception(f"Failed to fetch {statement_type} for {symbol}: {str(e)}")
    
    async def get_stock_price(self, symbol: str) -> DataSourceResponse:
        """Get current stock price"""
        cache_key = self.get_cache_key("stock_price", symbol=symbol)
        
        # Check cache first (2 minute TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=120)
        if cached_response:
            return cached_response
        
        async def _fetch_price():
            # Get recent price data
            hist = await self._run_in_executor(self._get_ticker_history, symbol, "2d")
            
            if hist.empty:
                raise Exception(f"No price data found for symbol {symbol}")
            
            # Get the most recent data
            latest = hist.iloc[-1]
            previous = hist.iloc[-2] if len(hist) > 1 else latest
            
            # Calculate change
            current_price = float(latest['Close'])
            previous_close = float(previous['Close'])
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100 if previous_close != 0 else 0
            
            price_data = {
                "symbol": symbol,
                "price": current_price,
                "change": change,
                "change_percent": change_percent,
                "volume": int(latest['Volume']),
                "high": float(latest['High']),
                "low": float(latest['Low']),
                "open": float(latest['Open']),
                "previous_close": previous_close,
                "timestamp": latest.name.to_pydatetime()
            }
            
            return DataSourceResponse(
                success=True,
                data=price_data,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_price)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_company_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get company fundamental data"""
        cache_key = self.get_cache_key("company_fundamentals", symbol=symbol)
        
        # Check cache first (24 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=86400)
        if cached_response:
            return cached_response
        
        async def _fetch_fundamentals():
            info = await self._run_in_executor(self._get_ticker_info, symbol)
            
            # Extract and structure fundamental data
            fundamentals = {
                "symbol": info.get("symbol", symbol),
                "name": info.get("longName") or info.get("shortName"),
                "sector": info.get("sector"),
                "industry": info.get("industry"),
                "description": info.get("longBusinessSummary"),
                "market_cap": info.get("marketCap"),
                "enterprise_value": info.get("enterpriseValue"),
                "pe_ratio": info.get("trailingPE"),
                "forward_pe": info.get("forwardPE"),
                "peg_ratio": info.get("pegRatio"),
                "price_to_book": info.get("priceToBook"),
                "price_to_sales": info.get("priceToSalesTrailing12Months"),
                "ev_to_revenue": info.get("enterpriseToRevenue"),
                "ev_to_ebitda": info.get("enterpriseToEbitda"),
                "book_value": info.get("bookValue"),
                "dividend_rate": info.get("dividendRate"),
                "dividend_yield": info.get("dividendYield"),
                "payout_ratio": info.get("payoutRatio"),
                "beta": info.get("beta"),
                "52_week_high": info.get("fiftyTwoWeekHigh"),
                "52_week_low": info.get("fiftyTwoWeekLow"),
                "50_day_average": info.get("fiftyDayAverage"),
                "200_day_average": info.get("twoHundredDayAverage"),
                "shares_outstanding": info.get("sharesOutstanding"),
                "float_shares": info.get("floatShares"),
                "held_percent_insiders": info.get("heldPercentInsiders"),
                "held_percent_institutions": info.get("heldPercentInstitutions"),
                "short_ratio": info.get("shortRatio"),
                "short_percent_of_float": info.get("shortPercentOfFloat"),
                "profit_margins": info.get("profitMargins"),
                "operating_margins": info.get("operatingMargins"),
                "return_on_assets": info.get("returnOnAssets"),
                "return_on_equity": info.get("returnOnEquity"),
                "revenue": info.get("totalRevenue"),
                "revenue_per_share": info.get("revenuePerShare"),
                "quarterly_revenue_growth": info.get("quarterlyRevenueGrowth"),
                "gross_profits": info.get("grossProfits"),
                "ebitda": info.get("ebitda"),
                "net_income_to_common": info.get("netIncomeToCommon"),
                "trailing_eps": info.get("trailingEps"),
                "forward_eps": info.get("forwardEps"),
                "quarterly_earnings_growth": info.get("quarterlyEarningsGrowth"),
                "earnings_date": info.get("earningsDate"),
                "ex_dividend_date": info.get("exDividendDate"),
                "last_dividend_date": info.get("lastDividendDate"),
                "current_ratio": info.get("currentRatio"),
                "quick_ratio": info.get("quickRatio"),
                "debt_to_equity": info.get("debtToEquity"),
                "total_cash": info.get("totalCash"),
                "total_debt": info.get("totalDebt"),
                "total_cash_per_share": info.get("totalCashPerShare"),
                "free_cashflow": info.get("freeCashflow"),
                "operating_cashflow": info.get("operatingCashflow"),
                "earnings_growth": info.get("earningsGrowth"),
                "revenue_growth": info.get("revenueGrowth"),
                "target_high_price": info.get("targetHighPrice"),
                "target_low_price": info.get("targetLowPrice"),
                "target_mean_price": info.get("targetMeanPrice"),
                "recommendation_mean": info.get("recommendationMean"),
                "number_of_analyst_opinions": info.get("numberOfAnalystOpinions"),
                "currency": info.get("currency"),
                "exchange": info.get("exchange"),
                "quote_type": info.get("quoteType"),
                "website": info.get("website"),
                "employees": info.get("fullTimeEmployees")
            }
            
            return DataSourceResponse(
                success=True,
                data=fundamentals,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_fundamentals)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_financial_statements(
        self, 
        symbol: str, 
        statement_type: str,
        period: str = "annual"
    ) -> DataSourceResponse:
        """Get financial statements"""
        cache_key = self.get_cache_key(
            "financial_statements", 
            symbol=symbol, 
            statement_type=statement_type,
            period=period
        )
        
        # Check cache first (24 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=86400)
        if cached_response:
            return cached_response
        
        async def _fetch_statements():
            quarterly = period == "quarterly"
            df = await self._run_in_executor(
                self._get_ticker_financials, 
                symbol, 
                statement_type, 
                quarterly
            )
            
            # Convert DataFrame to structured format
            reports = []
            for date, data in df.items():
                report = {
                    "fiscalDateEnding": date.strftime("%Y-%m-%d"),
                    "reportedCurrency": "USD"  # yfinance doesn't provide currency info
                }
                
                # Add financial data
                for index, value in data.items():
                    if pd.notna(value):
                        # Clean up index names for consistency
                        clean_key = str(index).replace(" ", "").replace("&", "And")
                        report[clean_key] = float(value) if isinstance(value, (int, float)) else str(value)
                
                reports.append(report)
            
            statements = {
                "symbol": symbol,
                "statement_type": statement_type,
                "period": period,
                "reports": reports[:5],  # Last 5 periods
                "metadata": {
                    "currency": "USD",
                    "last_updated": datetime.utcnow().isoformat()
                }
            }
            
            return DataSourceResponse(
                success=True,
                data=statements,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_statements)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> DataSourceResponse:
        """Get historical daily price data"""
        cache_key = self.get_cache_key(
            "historical_data",
            symbol=symbol,
            start_date=start_date.date().isoformat(),
            end_date=end_date.date().isoformat()
        )
        
        # Check cache first (1 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=3600)
        if cached_response:
            return cached_response
        
        async def _fetch_historical():
            def get_history():
                ticker = yf.Ticker(symbol)
                return ticker.history(start=start_date, end=end_date)
            
            hist = await self._run_in_executor(get_history)
            
            if hist.empty:
                raise Exception(f"No historical data found for symbol {symbol}")
            
            # Convert DataFrame to list of dictionaries
            historical_data = []
            for date, row in hist.iterrows():
                historical_data.append({
                    "date": date.strftime("%Y-%m-%d"),
                    "open": float(row['Open']),
                    "high": float(row['High']),
                    "low": float(row['Low']),
                    "close": float(row['Close']),
                    "volume": int(row['Volume']),
                    "dividends": float(row.get('Dividends', 0)),
                    "stock_splits": float(row.get('Stock Splits', 0))
                })
            
            # Sort by date (newest first)
            historical_data.sort(key=lambda x: x["date"], reverse=True)
            
            result = {
                "symbol": symbol,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "data": historical_data
            }
            
            return DataSourceResponse(
                success=True,
                data=result,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_historical)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
