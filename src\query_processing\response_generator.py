"""
Response Generator with OpenAI Integration
Generates natural language responses using structured prompts and data integration
"""
import asyncio
import openai
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from .parser import ParsedQuery, QueryType
from .executor import ExecutionResult
from ..config.settings import get_settings
from ..config.logging_config import get_logger


class ResponseGenerator:
    """Generates natural language responses using OpenAI with structured prompts"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("response_generator")
        self.client = openai.AsyncOpenAI(api_key=self.settings.openai_api_key)
        
        # Response templates for different query types
        self.prompt_templates = {
            QueryType.STOCK_PRICE: self._get_stock_price_prompt,
            QueryType.COMPANY_FUNDAMENTALS: self._get_fundamentals_prompt,
            QueryType.FINANCIAL_STATEMENTS: self._get_statements_prompt,
            QueryType.COMPARISON: self._get_comparison_prompt,
            QueryType.TREND_ANALYSIS: self._get_trend_analysis_prompt,
            QueryType.NEWS: self._get_news_prompt,
            QueryType.GENERAL: self._get_general_prompt
        }
    
    async def generate_response(
        self, 
        parsed_query: ParsedQuery, 
        execution_result: ExecutionResult
    ) -> str:
        """Generate natural language response from query and execution results"""
        
        self.logger.info(
            f"Generating response for {parsed_query.query_type.value} query "
            f"with {len(execution_result.data)} data sources"
        )
        
        try:
            # Handle execution failures
            if not execution_result.success:
                return self._generate_error_response(parsed_query, execution_result)
            
            # Get appropriate prompt template
            prompt_func = self.prompt_templates.get(
                parsed_query.query_type, 
                self._get_general_prompt
            )
            
            # Generate structured prompt
            system_prompt, user_prompt = prompt_func(parsed_query, execution_result)
            
            # Generate response using OpenAI
            response = await self._call_openai(system_prompt, user_prompt)
            
            # Post-process response
            final_response = self._post_process_response(response, execution_result)
            
            self.logger.info("Response generated successfully")
            return final_response
            
        except Exception as e:
            self.logger.error(f"Error generating response: {str(e)}")
            return self._generate_fallback_response(parsed_query, execution_result)
    
    async def _call_openai(self, system_prompt: str, user_prompt: str) -> str:
        """Call OpenAI API with structured prompts"""
        try:
            response = await self.client.chat.completions.create(
                model=self.settings.openai_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.settings.openai_max_tokens,
                temperature=0.3,  # Lower temperature for more factual responses
                top_p=0.9
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"OpenAI API call failed: {str(e)}")
            raise
    
    def _get_stock_price_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for stock price queries"""
        system_prompt = """You are a professional financial analyst assistant. 
        Provide clear, accurate information about stock prices based on the provided data.
        Include current price, change information, and brief context.
        Always mention the data source and timestamp for transparency."""
        
        # Extract stock price data
        price_data = {}
        for description, data in execution_result.data.items():
            if "stock price" in description.lower():
                symbol = description.split()[-1] if description.split() else "Unknown"
                price_data[symbol] = data
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Stock Price Data:
        {json.dumps(price_data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        
        Please provide a clear, professional response about the stock price(s) requested.
        Include the current price, any change information, and relevant context.
        """
        
        return system_prompt, user_prompt
    
    def _get_fundamentals_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for company fundamentals queries"""
        system_prompt = """You are a professional financial analyst assistant.
        Provide comprehensive analysis of company fundamentals based on the provided data.
        Focus on key metrics like P/E ratio, market cap, revenue, profitability, and growth.
        Explain what these metrics mean in simple terms and provide context."""
        
        fundamentals_data = {}
        for description, data in execution_result.data.items():
            if "fundamentals" in description.lower():
                symbol = description.split()[-1] if description.split() else "Unknown"
                fundamentals_data[symbol] = data
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Company Fundamentals Data:
        {json.dumps(fundamentals_data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        
        Please provide a comprehensive analysis of the company's fundamentals.
        Highlight key metrics and explain what they indicate about the company's financial health.
        """
        
        return system_prompt, user_prompt
    
    def _get_statements_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for financial statements queries"""
        system_prompt = """You are a professional financial analyst assistant.
        Analyze financial statement data and provide insights about the company's financial performance.
        Focus on trends, key line items, and what they reveal about the business.
        Explain financial concepts in accessible terms."""
        
        statements_data = {}
        for description, data in execution_result.data.items():
            if "statement" in description.lower():
                symbol = description.split()[-1] if description.split() else "Unknown"
                statements_data[symbol] = data
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Financial Statements Data:
        {json.dumps(statements_data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        
        Please analyze the financial statement data and provide insights about the company's performance.
        Highlight important trends and key metrics from the statements.
        """
        
        return system_prompt, user_prompt
    
    def _get_comparison_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for comparison queries"""
        system_prompt = """You are a professional financial analyst assistant.
        Compare multiple companies based on the provided financial data.
        Highlight key differences, strengths, and weaknesses of each company.
        Provide a balanced analysis focusing on the metrics most relevant to the comparison."""
        
        comparison_data = {}
        for description, data in execution_result.data.items():
            symbol = description.split()[-1] if description.split() else "Unknown"
            comparison_data[symbol] = data
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Companies to Compare: {', '.join(parsed_query.symbols)}
        
        Comparison Data:
        {json.dumps(comparison_data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        
        Please provide a detailed comparison of these companies.
        Highlight key differences in financial metrics and business performance.
        """
        
        return system_prompt, user_prompt
    
    def _get_trend_analysis_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for trend analysis queries"""
        system_prompt = """You are a professional financial analyst assistant.
        Analyze historical financial data to identify trends and patterns.
        Focus on performance over time, growth rates, and significant changes.
        Provide insights about what the trends might indicate for the future."""
        
        trend_data = {}
        for description, data in execution_result.data.items():
            if "historical" in description.lower():
                symbol = description.split()[-1] if description.split() else "Unknown"
                trend_data[symbol] = data
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Historical Data for Trend Analysis:
        {json.dumps(trend_data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        
        Please analyze the historical data and identify key trends.
        Discuss performance patterns, growth rates, and significant changes over time.
        """
        
        return system_prompt, user_prompt
    
    def _get_news_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for news queries"""
        system_prompt = """You are a professional financial news analyst.
        Summarize and analyze financial news based on the provided data.
        Focus on the most important developments and their potential impact.
        Provide context and explain the significance of the news."""
        
        news_data = {}
        for description, data in execution_result.data.items():
            if "news" in description.lower():
                symbol = description.split()[-1] if description.split() else "Unknown"
                news_data[symbol] = data
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Financial News Data:
        {json.dumps(news_data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        
        Please summarize the most important financial news and provide analysis.
        Focus on developments that could impact the company or market.
        """
        
        return system_prompt, user_prompt
    
    def _get_general_prompt(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> tuple:
        """Generate prompts for general queries"""
        system_prompt = """You are a professional financial analyst assistant.
        Provide helpful financial information and analysis based on the available data.
        If the data is limited, explain what you can determine and suggest additional information that might be helpful.
        Always be clear about the limitations of the available data."""
        
        user_prompt = f"""
        User Query: {parsed_query.original_query}
        
        Available Data:
        {json.dumps(execution_result.data, indent=2, default=str)}
        
        Sources Used: {', '.join(execution_result.sources_used)}
        Symbols Identified: {', '.join(parsed_query.symbols) if parsed_query.symbols else 'None'}
        
        Please provide a helpful response based on the available data.
        If the data is insufficient, explain what additional information would be needed.
        """
        
        return system_prompt, user_prompt
    
    def _generate_error_response(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> str:
        """Generate response for failed executions"""
        errors = execution_result.errors
        
        if "rate limit" in str(errors).lower():
            return (
                "I'm currently experiencing rate limiting from our data providers. "
                "Please try your query again in a few minutes. "
                f"Query: {parsed_query.original_query}"
            )
        elif "not found" in str(errors).lower():
            return (
                f"I couldn't find data for the requested information. "
                f"Please check if the stock symbols are correct: {', '.join(parsed_query.symbols)}. "
                f"Query: {parsed_query.original_query}"
            )
        else:
            return (
                f"I encountered an issue retrieving the requested financial data. "
                f"This might be due to temporary service issues with our data providers. "
                f"Please try again later. Query: {parsed_query.original_query}"
            )
    
    def _generate_fallback_response(self, parsed_query: ParsedQuery, execution_result: ExecutionResult) -> str:
        """Generate fallback response when OpenAI fails"""
        if execution_result.success and execution_result.data:
            # Try to provide basic information from the data
            response_parts = [f"Based on your query: {parsed_query.original_query}"]
            
            for description, data in execution_result.data.items():
                if isinstance(data, dict):
                    # Extract key information
                    key_info = []
                    for key, value in data.items():
                        if key in ['price', 'name', 'symbol', 'market_cap', 'revenue']:
                            key_info.append(f"{key}: {value}")
                    
                    if key_info:
                        response_parts.append(f"{description}: {', '.join(key_info[:3])}")
            
            response_parts.append(f"Data sources: {', '.join(execution_result.sources_used)}")
            return "\n".join(response_parts)
        else:
            return self._generate_error_response(parsed_query, execution_result)
    
    def _post_process_response(self, response: str, execution_result: ExecutionResult) -> str:
        """Post-process the generated response"""
        # Add source attribution if not already present
        if execution_result.sources_used and "source" not in response.lower():
            response += f"\n\n*Data sources: {', '.join(execution_result.sources_used)}*"
        
        # Add timestamp
        timestamp = datetime.utcnow().strftime("%Y-%m-%d %H:%M UTC")
        response += f"\n\n*Generated at: {timestamp}*"
        
        return response
