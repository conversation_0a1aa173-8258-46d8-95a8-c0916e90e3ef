"""
Qdrant Cloud Client
Manages vector database operations for document embeddings and similarity search
Uses Qdrant Cloud hosted service (no Docker required)
"""
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import uuid

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

from ..config.settings import get_settings
from ..config.logging_config import get_logger


class QdrantVectorDB:
    """Qdrant Cloud vector database client"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("qdrant")
        self.client = None
        self.collection_name = self.settings.qdrant_collection_name
        
    async def initialize(self):
        """Initialize Qdrant client and ensure collection exists"""
        try:
            self.client = QdrantClient(
                url=self.settings.qdrant_url,
                api_key=self.settings.qdrant_api_key,
                timeout=30
            )
            
            # Test connection
            collections = self.client.get_collections()
            self.logger.info(f"Connected to Qdrant Cloud. Found {len(collections.collections)} collections")
            
            # Ensure our collection exists
            await self._ensure_collection_exists()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Qdrant client: {str(e)}")
            raise
    
    async def _ensure_collection_exists(self):
        """Create collection if it doesn't exist"""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                self.logger.info(f"Creating collection: {self.collection_name}")
                
                # Create collection with vector configuration
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.settings.embedding_dimension,  # OpenAI text-embedding-3-small dimension
                        distance=Distance.COSINE
                    )
                )
                
                # Create indexes for better search performance
                self.client.create_payload_index(
                    collection_name=self.collection_name,
                    field_name="document_type",
                    field_schema=models.KeywordIndexParams(
                        type="keyword",
                        is_tenant=False
                    )
                )
                
                self.client.create_payload_index(
                    collection_name=self.collection_name,
                    field_name="company_symbol",
                    field_schema=models.KeywordIndexParams(
                        type="keyword",
                        is_tenant=False
                    )
                )
                
                self.logger.info(f"Collection {self.collection_name} created successfully")
            else:
                self.logger.info(f"Collection {self.collection_name} already exists")
                
        except Exception as e:
            self.logger.error(f"Error ensuring collection exists: {str(e)}")
            raise
    
    async def add_document_chunks(
        self,
        chunks: List[str],
        embeddings: List[List[float]],
        metadata: Dict[str, Any]
    ) -> List[str]:
        """Add document chunks with embeddings to the vector database"""
        if len(chunks) != len(embeddings):
            raise ValueError("Number of chunks must match number of embeddings")
        
        try:
            points = []
            chunk_ids = []
            
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                chunk_id = str(uuid.uuid4())
                chunk_ids.append(chunk_id)
                
                # Prepare payload with metadata
                payload = {
                    "text": chunk,
                    "chunk_index": i,
                    "document_id": metadata.get("document_id"),
                    "filename": metadata.get("filename"),
                    "document_type": metadata.get("document_type"),
                    "company_symbol": metadata.get("company_symbol"),
                    "filing_date": metadata.get("filing_date"),
                    "page_number": metadata.get("page_number"),
                    "section": metadata.get("section"),
                    "created_at": datetime.utcnow().isoformat(),
                    "chunk_length": len(chunk)
                }
                
                # Create point
                point = PointStruct(
                    id=chunk_id,
                    vector=embedding,
                    payload=payload
                )
                points.append(point)
            
            # Batch insert points
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            self.logger.info(f"Added {len(points)} chunks to vector database")
            return chunk_ids
            
        except Exception as e:
            self.logger.error(f"Error adding document chunks: {str(e)}")
            raise
    
    async def search_similar_chunks(
        self,
        query_embedding: List[float],
        limit: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar chunks using vector similarity"""
        try:
            # Build filter conditions
            filter_conditions = None
            if filters:
                conditions = []
                
                if "company_symbol" in filters:
                    conditions.append(
                        FieldCondition(
                            key="company_symbol",
                            match=MatchValue(value=filters["company_symbol"])
                        )
                    )
                
                if "document_type" in filters:
                    conditions.append(
                        FieldCondition(
                            key="document_type",
                            match=MatchValue(value=filters["document_type"])
                        )
                    )
                
                if conditions:
                    filter_conditions = Filter(must=conditions)
            
            # Perform search
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter=filter_conditions,
                limit=limit,
                score_threshold=score_threshold or self.settings.similarity_threshold
            )
            
            # Format results
            results = []
            for scored_point in search_result:
                result = {
                    "id": scored_point.id,
                    "score": scored_point.score,
                    "text": scored_point.payload.get("text"),
                    "metadata": {
                        "document_id": scored_point.payload.get("document_id"),
                        "filename": scored_point.payload.get("filename"),
                        "document_type": scored_point.payload.get("document_type"),
                        "company_symbol": scored_point.payload.get("company_symbol"),
                        "filing_date": scored_point.payload.get("filing_date"),
                        "page_number": scored_point.payload.get("page_number"),
                        "section": scored_point.payload.get("section"),
                        "chunk_index": scored_point.payload.get("chunk_index"),
                        "chunk_length": scored_point.payload.get("chunk_length")
                    }
                }
                results.append(result)
            
            self.logger.info(f"Found {len(results)} similar chunks")
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching similar chunks: {str(e)}")
            raise
    
    async def get_document_chunks(self, document_id: str) -> List[Dict[str, Any]]:
        """Get all chunks for a specific document"""
        try:
            # Search with document_id filter
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=document_id)
                    )
                ]
            )
            
            # Use scroll to get all chunks (not limited by search limit)
            chunks = []
            offset = None
            
            while True:
                result = self.client.scroll(
                    collection_name=self.collection_name,
                    scroll_filter=filter_condition,
                    limit=100,
                    offset=offset
                )
                
                if not result[0]:  # No more points
                    break
                
                for point in result[0]:
                    chunk = {
                        "id": point.id,
                        "text": point.payload.get("text"),
                        "chunk_index": point.payload.get("chunk_index"),
                        "metadata": {
                            "document_id": point.payload.get("document_id"),
                            "filename": point.payload.get("filename"),
                            "document_type": point.payload.get("document_type"),
                            "company_symbol": point.payload.get("company_symbol"),
                            "filing_date": point.payload.get("filing_date"),
                            "page_number": point.payload.get("page_number"),
                            "section": point.payload.get("section")
                        }
                    }
                    chunks.append(chunk)
                
                offset = result[1]  # Next offset
            
            # Sort by chunk_index
            chunks.sort(key=lambda x: x.get("chunk_index", 0))
            
            self.logger.info(f"Retrieved {len(chunks)} chunks for document {document_id}")
            return chunks
            
        except Exception as e:
            self.logger.error(f"Error getting document chunks: {str(e)}")
            raise
    
    async def delete_document(self, document_id: str) -> int:
        """Delete all chunks for a specific document"""
        try:
            # Delete points with matching document_id
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=document_id)
                    )
                ]
            )
            
            result = self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.FilterSelector(filter=filter_condition)
            )
            
            deleted_count = result.operation_id  # This might need adjustment based on Qdrant response
            self.logger.info(f"Deleted {deleted_count} chunks for document {document_id}")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Error deleting document: {str(e)}")
            raise
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection"""
        try:
            info = self.client.get_collection(self.collection_name)
            
            collection_info = {
                "name": info.config.params.vectors.size if hasattr(info.config.params, 'vectors') else 'unknown',
                "vector_size": info.config.params.vectors.size if hasattr(info.config.params, 'vectors') else 0,
                "distance": info.config.params.vectors.distance if hasattr(info.config.params, 'vectors') else 'unknown',
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "status": info.status
            }
            
            return collection_info
            
        except Exception as e:
            self.logger.error(f"Error getting collection info: {str(e)}")
            raise
    
    async def health_check(self) -> bool:
        """Check if Qdrant service is healthy"""
        try:
            collections = self.client.get_collections()
            return True
        except Exception as e:
            self.logger.error(f"Qdrant health check failed: {str(e)}")
            return False
