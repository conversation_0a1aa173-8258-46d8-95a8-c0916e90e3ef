"""
Alpha Vantage API Client
Provides access to company fundamentals, financial statements, and market data
Rate limit: 25 requests per day (free tier)
"""
import asyncio
import aiohttp
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import json

from .base import BaseDataSource, DataSourceResponse, DataSourceType, RateLimitInfo
from ..config.logging_config import get_logger


class AlphaVantageClient(BaseDataSource):
    """Alpha Vantage API client with rate limiting and error handling"""
    
    BASE_URL = "https://www.alphavantage.co/query"
    
    def __init__(self, api_key: str):
        rate_limit_info = RateLimitInfo(
            requests_per_minute=5,  # Conservative to avoid hitting daily limit
            requests_per_day=25
        )
        
        super().__init__(
            name="Alpha Vantage",
            source_type=DataSourceType.PRIMARY,
            api_key=api_key,
            rate_limit_info=rate_limit_info
        )
        
        self.logger = get_logger("alpha_vantage")
    
    async def _make_request(self, params: Dict[str, str]) -> Dict[str, Any]:
        """Make HTTP request to Alpha Vantage API"""
        params["apikey"] = self.api_key
        
        async with aiohttp.ClientSession() as session:
            async with session.get(self.BASE_URL, params=params) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {await response.text()}")
                
                data = await response.json()
                
                # Check for API errors
                if "Error Message" in data:
                    raise Exception(f"API Error: {data['Error Message']}")
                
                if "Note" in data:
                    raise Exception(f"Rate limit exceeded: {data['Note']}")
                
                return data
    
    async def get_stock_price(self, symbol: str) -> DataSourceResponse:
        """Get current stock price using Global Quote endpoint"""
        cache_key = self.get_cache_key("stock_price", symbol=symbol)
        
        # Check cache first (5 minute TTL for stock prices)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=300)
        if cached_response:
            return cached_response
        
        async def _fetch_price():
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": symbol
            }
            
            data = await self._make_request(params)
            
            if "Global Quote" not in data:
                raise Exception("Invalid response format from Alpha Vantage")
            
            quote = data["Global Quote"]
            
            if not quote:
                raise Exception(f"No data found for symbol {symbol}")
            
            # Parse the response
            price_data = {
                "symbol": quote.get("01. symbol", symbol),
                "price": float(quote.get("05. price", 0)),
                "change": float(quote.get("09. change", 0)),
                "change_percent": quote.get("10. change percent", "0%").replace("%", ""),
                "volume": int(quote.get("06. volume", 0)),
                "latest_trading_day": quote.get("07. latest trading day"),
                "previous_close": float(quote.get("08. previous close", 0))
            }
            
            return DataSourceResponse(
                success=True,
                data=price_data,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_price)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_company_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get company overview and fundamental data"""
        cache_key = self.get_cache_key("company_fundamentals", symbol=symbol)
        
        # Check cache first (24 hour TTL for fundamentals)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=86400)
        if cached_response:
            return cached_response
        
        async def _fetch_fundamentals():
            params = {
                "function": "OVERVIEW",
                "symbol": symbol
            }
            
            data = await self._make_request(params)
            
            if not data or "Symbol" not in data:
                raise Exception(f"No fundamental data found for symbol {symbol}")
            
            # Parse and structure the fundamental data
            fundamentals = {
                "symbol": data.get("Symbol"),
                "name": data.get("Name"),
                "description": data.get("Description"),
                "sector": data.get("Sector"),
                "industry": data.get("Industry"),
                "market_cap": self._safe_float(data.get("MarketCapitalization")),
                "pe_ratio": self._safe_float(data.get("PERatio")),
                "peg_ratio": self._safe_float(data.get("PEGRatio")),
                "book_value": self._safe_float(data.get("BookValue")),
                "dividend_per_share": self._safe_float(data.get("DividendPerShare")),
                "dividend_yield": self._safe_float(data.get("DividendYield")),
                "eps": self._safe_float(data.get("EPS")),
                "revenue_per_share": self._safe_float(data.get("RevenuePerShareTTM")),
                "profit_margin": self._safe_float(data.get("ProfitMargin")),
                "operating_margin": self._safe_float(data.get("OperatingMarginTTM")),
                "return_on_assets": self._safe_float(data.get("ReturnOnAssetsTTM")),
                "return_on_equity": self._safe_float(data.get("ReturnOnEquityTTM")),
                "revenue_ttm": self._safe_float(data.get("RevenueTTM")),
                "gross_profit_ttm": self._safe_float(data.get("GrossProfitTTM")),
                "ebitda": self._safe_float(data.get("EBITDA")),
                "52_week_high": self._safe_float(data.get("52WeekHigh")),
                "52_week_low": self._safe_float(data.get("52WeekLow")),
                "beta": self._safe_float(data.get("Beta")),
                "shares_outstanding": self._safe_float(data.get("SharesOutstanding")),
                "exchange": data.get("Exchange"),
                "currency": data.get("Currency"),
                "country": data.get("Country")
            }
            
            return DataSourceResponse(
                success=True,
                data=fundamentals,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_fundamentals)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_financial_statements(
        self, 
        symbol: str, 
        statement_type: str,
        period: str = "annual"
    ) -> DataSourceResponse:
        """Get financial statements (income, balance, cash flow)"""
        cache_key = self.get_cache_key(
            "financial_statements", 
            symbol=symbol, 
            statement_type=statement_type,
            period=period
        )
        
        # Check cache first (24 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=86400)
        if cached_response:
            return cached_response
        
        # Map statement types to Alpha Vantage function names
        function_map = {
            "income": "INCOME_STATEMENT",
            "balance": "BALANCE_SHEET",
            "cash_flow": "CASH_FLOW"
        }
        
        if statement_type not in function_map:
            return DataSourceResponse(
                success=False,
                error=f"Invalid statement type: {statement_type}",
                source=self.name
            )
        
        async def _fetch_statements():
            params = {
                "function": function_map[statement_type],
                "symbol": symbol
            }
            
            data = await self._make_request(params)
            
            # Get the appropriate data key
            data_key = f"{period}Reports"
            if data_key not in data:
                raise Exception(f"No {statement_type} data found for symbol {symbol}")
            
            reports = data[data_key]
            if not reports:
                raise Exception(f"No {period} {statement_type} reports found")
            
            # Structure the financial statement data
            statements = {
                "symbol": symbol,
                "statement_type": statement_type,
                "period": period,
                "reports": reports[:5],  # Last 5 periods
                "metadata": {
                    "currency": data.get("currency", "USD"),
                    "last_updated": datetime.utcnow().isoformat()
                }
            }
            
            return DataSourceResponse(
                success=True,
                data=statements,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_statements)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> DataSourceResponse:
        """Get historical daily price data"""
        cache_key = self.get_cache_key(
            "historical_data",
            symbol=symbol,
            start_date=start_date.date().isoformat(),
            end_date=end_date.date().isoformat()
        )
        
        # Check cache first (1 hour TTL)
        cached_response = self.get_cached_response(cache_key, ttl_seconds=3600)
        if cached_response:
            return cached_response
        
        async def _fetch_historical():
            params = {
                "function": "TIME_SERIES_DAILY_ADJUSTED",
                "symbol": symbol,
                "outputsize": "full"
            }
            
            data = await self._make_request(params)
            
            if "Time Series (Daily)" not in data:
                raise Exception(f"No historical data found for symbol {symbol}")
            
            time_series = data["Time Series (Daily)"]
            
            # Filter data by date range and convert to list
            historical_data = []
            for date_str, values in time_series.items():
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                
                if start_date <= date_obj <= end_date:
                    historical_data.append({
                        "date": date_str,
                        "open": float(values["1. open"]),
                        "high": float(values["2. high"]),
                        "low": float(values["3. low"]),
                        "close": float(values["4. close"]),
                        "adjusted_close": float(values["5. adjusted close"]),
                        "volume": int(values["6. volume"]),
                        "dividend_amount": float(values["7. dividend amount"]),
                        "split_coefficient": float(values["8. split coefficient"])
                    })
            
            # Sort by date (newest first)
            historical_data.sort(key=lambda x: x["date"], reverse=True)
            
            result = {
                "symbol": symbol,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "data": historical_data,
                "metadata": data.get("Meta Data", {})
            }
            
            return DataSourceResponse(
                success=True,
                data=result,
                source=self.name,
                timestamp=datetime.utcnow()
            )
        
        response = await self._execute_with_circuit_breaker(_fetch_historical)
        
        if response.success:
            self.cache_response(cache_key, response)
        
        return response
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """Safely convert value to float"""
        if value is None or value == "None" or value == "":
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
